from typing import Dict, Any

import requests
import io
import PyPDF2

from logging import getLogger

logger = getLogger(__name__)

def extract_text_from_pdf(pdf_file) -> str:
    """Extract text content from a PDF file."""
    try:
        reader = PyPDF2.PdfReader(pdf_file)
        text = ""
        for page in reader.pages:
            text += page.extract_text() + "\n"
        return text.strip()
    except Exception as e:
        logger.error(f"Failed to extract text from PDF: {str(e)}")
        raise ValueError(f"Failed to extract text from PDF: {str(e)}")

async def read_pdf_url(url: str) -> Dict[str, Any]:
    """Read text content from a PDF URL."""
    try:
        response = requests.get(url)
        response.raise_for_status()
        pdf_file = io.BytesIO(response.content)
        text = extract_text_from_pdf(pdf_file)
        return {
            "success": True,
            "data": {
                "text": text
            }
        }
    except requests.RequestException as e:
        logger.error(f"Failed to fetch PDF from URL: {str(e)}")
        return {
            "success": False,
            "error": f"Failed to fetch PDF from URL: {str(e)}"
        }
    except Exception as e:
        logger.error(str(e))
        return {
            "success": False,
            "error": str(e)
        }

print(read_pdf_url("https://arxiv.org/pdf/2301.00001.pdf"))