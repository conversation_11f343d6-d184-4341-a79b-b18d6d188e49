<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>太阳系模拟</title>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        #canvas {
            width: 100%;
            height: 100vh;
        }
        #controls {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 8px;
            color: white;
        }
        button {
            margin: 5px;
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            background: #4CAF50;
            color: white;
            cursor: pointer;
            transition: background 0.3s;
        }
        button:hover {
            background: #45a049;
        }
        #labels {
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div id="controls">
        <button onclick="setSpeed(0.5)">慢速</button>
        <button onclick="setSpeed(1)">正常</button>
        <button onclick="setSpeed(2)">快速</button>
        <button onclick="toggleLabels()">显示/隐藏标签</button>
        <button onclick="toggleOrbits()">显示/隐藏轨道</button>
    </div>
    <div id="labels"></div>
    <div id="canvas"></div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        let scene, camera, renderer, controls;
        let planets = [];
        let orbits = [];
        let labels = [];
        let speedFactor = 1;
        let showLabels = true;
        let showOrbits = true;

        // 行星数据
        const planetData = [
            { name: '太阳', radius: 50, color: 0xffff00, orbitRadius: 0, rotationSpeed: 0 },
            { name: '水星', radius: 3.8, color: 0x8B7355, orbitRadius: 80, rotationSpeed: 4.787 },
            { name: '金星', radius: 9.5, color: 0xFAF0E6, orbitRadius: 120, rotationSpeed: 3.502 },
            { name: '地球', radius: 10, color: 0x4169E1, orbitRadius: 160, rotationSpeed: 2.978 },
            { name: '火星', radius: 5.3, color: 0xFF4500, orbitRadius: 200, rotationSpeed: 2.407 },
            { name: '木星', radius: 30, color: 0xDEB887, orbitRadius: 280, rotationSpeed: 1.307 },
            { name: '土星', radius: 25, color: 0xFFE4B5, orbitRadius: 350, rotationSpeed: 0.969 },
            { name: '天王星', radius: 15, color: 0x40E0D0, orbitRadius: 400, rotationSpeed: 0.681 },
            { name: '海王星', radius: 14.5, color: 0x0000FF, orbitRadius: 450, rotationSpeed: 0.543 },
            { name: '冥王星', radius: 2.8, color: 0x8B7355, orbitRadius: 490, rotationSpeed: 0.474 }
        ];

        function init() {
            // 创建场景
            scene = new THREE.Scene();

            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 2000);
            camera.position.z = 500;
            camera.position.y = 200;

            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            document.getElementById('canvas').appendChild(renderer.domElement);

            // 添加星空背景
            createStarfield();

            // 创建行星
            createPlanets();

            // 创建轨道
            createOrbits();

            // 添加环境光和点光源
            const ambientLight = new THREE.AmbientLight(0x404040);
            scene.add(ambientLight);

            const pointLight = new THREE.PointLight(0xffffff, 2, 1000);
            scene.add(pointLight);

            // 创建标签
            createLabels();

            // 开始动画
            animate();
        }

        function createStarfield() {
            const starsGeometry = new THREE.BufferGeometry();
            const starsMaterial = new THREE.PointsMaterial({
                color: 0xFFFFFF,
                size: 0.5
            });

            const starsVertices = [];
            for (let i = 0; i < 5000; i++) {
                const x = (Math.random() - 0.5) * 2000;
                const y = (Math.random() - 0.5) * 2000;
                const z = (Math.random() - 0.5) * 2000;
                starsVertices.push(x, y, z);
            }

            starsGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starsVertices, 3));
            const starField = new THREE.Points(starsGeometry, starsMaterial);
            scene.add(starField);
        }

        function createPlanets() {
            planetData.forEach((data, index) => {
                const geometry = new THREE.SphereGeometry(data.radius, 32, 32);
                const material = new THREE.MeshPhongMaterial({
                    color: data.color,
                    shininess: 30
                });

                const planet = new THREE.Mesh(geometry, material);
                
                if (index === 0) { // 太阳
                    const sunLight = new THREE.PointLight(0xffffff, 2, 1000);
                    planet.add(sunLight);
                }

                if (index === 5) { // 土星
                    const ringGeometry = new THREE.RingGeometry(data.radius * 1.4, data.radius * 1.8, 32);
                    const ringMaterial = new THREE.MeshBasicMaterial({
                        color: 0xA0522D,
                        side: THREE.DoubleSide,
                        transparent: true,
                        opacity: 0.6
                    });
                    const ring = new THREE.Mesh(ringGeometry, ringMaterial);
                    ring.rotation.x = Math.PI / 2;
                    planet.add(ring);
                }

                planet.position.x = data.orbitRadius;
                planets.push({
                    mesh: planet,
                    orbitRadius: data.orbitRadius,
                    rotationSpeed: data.rotationSpeed,
                    angle: Math.random() * Math.PI * 2
                });
                scene.add(planet);
            });
        }

        function createOrbits() {
            planetData.forEach((data, index) => {
                if (index === 0) return; // 跳过太阳
                const orbitGeometry = new THREE.RingGeometry(data.orbitRadius - 0.5, data.orbitRadius + 0.5, 128);
                const orbitMaterial = new THREE.MeshBasicMaterial({
                    color: 0x666666,
                    side: THREE.DoubleSide,
                    transparent: true,
                    opacity: 0.3
                });
                const orbit = new THREE.Mesh(orbitGeometry, orbitMaterial);
                orbit.rotation.x = Math.PI / 2;
                orbits.push(orbit);
                scene.add(orbit);
            });
        }

        function createLabels() {
            const labelsContainer = document.getElementById('labels');
            planets.forEach((planet, index) => {
                const label = document.createElement('div');
                label.style.position = 'absolute';
                label.style.color = 'white';
                label.style.padding = '2px 6px';
                label.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
                label.style.borderRadius = '4px';
                label.style.fontSize = '12px';
                label.textContent = planetData[index].name;
                labelsContainer.appendChild(label);
                labels.push(label);
            });
        }

        function updateLabels() {
            planets.forEach((planet, index) => {
                if (!labels[index]) return;
                
                const vector = new THREE.Vector3();
                vector.setFromMatrixPosition(planet.mesh.matrixWorld);
                vector.project(camera);

                const x = (vector.x * 0.5 + 0.5) * window.innerWidth;
                const y = (-vector.y * 0.5 + 0.5) * window.innerHeight;

                labels[index].style.transform = `translate(-50%, -50%) translate(${x}px, ${y}px)`;
                labels[index].style.display = showLabels ? 'block' : 'none';
            });
        }

        function animate() {
            requestAnimationFrame(animate);

            // 更新行星位置
            planets.forEach((planet, index) => {
                if (index === 0) return; // 跳过太阳
                planet.angle += (planet.rotationSpeed * 0.001 * speedFactor);
                planet.mesh.position.x = Math.cos(planet.angle) * planet.orbitRadius;
                planet.mesh.position.z = Math.sin(planet.angle) * planet.orbitRadius;
                planet.mesh.rotation.y += 0.01;
            });

            // 太阳自转和脉动效果
            planets[0].mesh.rotation.y += 0.001;
            const pulseFactor = 1 + Math.sin(Date.now() * 0.001) * 0.02;
            planets[0].mesh.scale.set(pulseFactor, pulseFactor, pulseFactor);

            // 更新标签位置
            updateLabels();

            // 更新轨道可见性
            orbits.forEach(orbit => {
                orbit.visible = showOrbits;
            });

            renderer.render(scene, camera);
        }

        function setSpeed(speed) {
            speedFactor = speed;
        }

        function toggleLabels() {
            showLabels = !showLabels;
        }

        function toggleOrbits() {
            showOrbits = !showOrbits;
        }

        // 窗口大小调整处理
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

        // 初始化场景
        init();
    </script>
</body>
</html>