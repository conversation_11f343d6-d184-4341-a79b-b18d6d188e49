import hmac
import json

import requests


def verify_sms_request(lot_number,pass_token,gen_time,captcha_output):

    captcha_id = "5b96fef3516676f28fb14b1088c37bd4"
    captcha_key = "b55e6a59b3b77f6f71904e6a98cbf1f2"
    api_server = "https://captcha.alicaptcha.com"


    lotnumber_bytes = lot_number.encode()
    prikey_bytes = captcha_key.encode()
    sign_token = hmac.new(prikey_bytes, lotnumber_bytes, digestmod="SHA256").hexdigest()
    print(sign_token)

    query = {
        "lot_number": lot_number,
        "captcha_output": captcha_output,
        "pass_token": pass_token,
        "gen_time": gen_time,
        "sign_token": sign_token,
    }

    url = api_server + "/validate" + "?captcha_id={}".format(captcha_id)

    try:
        res = requests.post(url, data=query)
        assert res.status_code == 200
        msg = json.loads(res.text)
    except Exception as e:
        msg = {"result": "error", "reason": "request api fail",'error_trace':str(e)}

    # 5.根据⼆次验证接口返回的用户验证状态, 完成您自己的业务逻辑
    if msg["result"] == "success":
        return True
    else:
        return False




verify_sms_request(
    "ce34b077dcd34382bcb02a231272f48b",
    "574941bf83abd3a09a43f606e5d1ff602e7209294ac72faa059889f56f7935b3",
    "1753864077",
    "5qAtVjP74w5pzmwvitsA7QMw2J7vpkw8obTmdev8plA_Mhgdd4kTZENW3mHb35cpmlyiQv1D5Btv8CWQk9jjDNx9tClkj6xn6rtDNms594n-UlhJQzal9qYbjsjhO9z53XxDrDrQX15MAW6idrklYO8ZnY419W8W7sjGJlxQ4nzFuItfiZACOdZuErNaZxov5UquXy_pAcOi9hhAjb9dBI-yVD2gChYZP8_mgYzBUcfPMN-zzEDZG-Zh0L9DMPGXVtsntUTpk75QUf9LZL2HVMGy1V-2kbq-KWuBKCmdrqDyZ8_xaKR4edLpvr38q_qI3ewpzOEFH9BqVOiDWqWN08G59zg9T_SXbzFhrJUJQoMmkHvmpKdLBpJg1GTJSyVP3RwRqjTPL_wzBKAUjtRE4CVFilSvXtdckFXJ7-3froh5W2CPZPAaMU2YPWEmva66zSo3CtN1IE-NKjcZV3aTdPQzUlzhrE4wvUGqnnvVEC8YhgOstlvvPGDgA7q4B0X0wMzwgpTPFJivQMLstYVPhI4PZlOam624SO6FYIvyoyyLNWqW_4KHEizJbxGjkR2zjrvUjPEHhebxkNpCzMQuVA0V9OzjXueGOK9CYBxAkxwHtD2W9vIhhiPJzcoMMQSJIf7iRMtpZfgleoNx8QBnOQ=="
)