package main

import (
	"bytes"
	"fmt"
	"os/exec"
	"strconv"
	"strings"
)

// CheckSpamScore 检查邮件的垃圾分数
// 返回垃圾邮件分数和可能的错误
// 如果返回的错误为nil，则分数有效
func CheckSpamScore(emailContent string) (float64, error) {
	cmd := exec.Command("spamc", "-c")
	cmd.Stdin = bytes.NewBufferString(emailContent)

	output, err := cmd.CombinedOutput()
	result := strings.TrimSpace(string(output))

	// 解析输出结果
	parts := strings.Split(result, "/")
	if len(parts) != 2 {
		if err != nil {
			return 0, fmt.Errorf("执行spamc失败: %v, 输出: %s", err, result)
		}
		return 0, fmt.Errorf("无法解析spamc返回结果: %s", result)
	}

	// 转换分数为float64
	score, err := strconv.ParseFloat(parts[0], 64)
	if err != nil {
		return 0, fmt.Errorf("解析分数失败: %v", err)
	}

	return score, nil
}

func main() {
	//emailContent := ``
	//
	//score, err := CheckSpamScore(emailContent)
	//if err != nil {
	//	fmt.Printf("检查垃圾邮件失败: %v\n", err)
	//	return
	//}
	//
	//fmt.Printf("邮件垃圾分数: %.2f\n", score)
	//fmt.Printf("是否属于垃圾邮件: %v\n", score > 5)

	fmt.Println(float64(13.7) > 5)
}
