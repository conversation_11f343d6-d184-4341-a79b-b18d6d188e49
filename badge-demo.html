<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>徽章演示 - Badge Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            line-height: 1.6;
            background: #f8f9fa;
        }

        .container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .badge-section {
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .badge-section h2 {
            color: #555;
            margin-bottom: 15px;
        }

        .badge-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .badge-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 6px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .badge-item img {
            margin-bottom: 10px;
        }

        .badge-item code {
            display: block;
            background: #e9ecef;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            word-break: break-all;
            margin-top: 10px;
        }

        .generator-link {
            text-align: center;
            margin: 40px 0;
            padding: 30px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 10px;
            color: white;
        }

        .generator-link a {
            color: white;
            text-decoration: none;
            font-size: 18px;
            font-weight: bold;
            display: inline-block;
            padding: 15px 30px;
            background: rgba(255,255,255,0.2);
            border-radius: 8px;
            transition: all 0.3s;
        }

        .generator-link a:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .usage-example {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .usage-example h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }

        .usage-example pre {
            background: #263238;
            color: #fff;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏆 徽章演示页面</h1>
        
        <div class="generator-link">
            <h2>🚀 开始创建您的徽章</h2>
            <p>使用我们的徽章生成器，轻松创建专业的项目徽章</p>
            <a href="badge-generator.html">打开徽章生成器</a>
        </div>

        <div class="badge-section">
            <h2>📊 项目状态徽章</h2>
            <div class="badge-grid">
                <div class="badge-item">
                    <img src="https://img.shields.io/badge/Build-Passing-brightgreen" alt="Build Status">
                    <div>构建状态</div>
                    <code>https://img.shields.io/badge/Build-Passing-brightgreen</code>
                </div>
                <div class="badge-item">
                    <img src="https://img.shields.io/badge/Tests-95%25-green" alt="Test Coverage">
                    <div>测试覆盖率</div>
                    <code>https://img.shields.io/badge/Tests-95%25-green</code>
                </div>
                <div class="badge-item">
                    <img src="https://img.shields.io/badge/Version-v2.1.0-blue" alt="Version">
                    <div>版本信息</div>
                    <code>https://img.shields.io/badge/Version-v2.1.0-blue</code>
                </div>
                <div class="badge-item">
                    <img src="https://img.shields.io/badge/License-MIT-yellow" alt="License">
                    <div>许可证</div>
                    <code>https://img.shields.io/badge/License-MIT-yellow</code>
                </div>
            </div>
        </div>

        <div class="badge-section">
            <h2>🛠️ 技术栈徽章</h2>
            <div class="badge-grid">
                <div class="badge-item">
                    <img src="https://img.shields.io/badge/JavaScript-F7DF1E?style=for-the-badge&logo=javascript&logoColor=black" alt="JavaScript">
                    <div>JavaScript</div>
                    <code>https://img.shields.io/badge/JavaScript-F7DF1E?style=for-the-badge&logo=javascript&logoColor=black</code>
                </div>
                <div class="badge-item">
                    <img src="https://img.shields.io/badge/React-20232A?style=for-the-badge&logo=react&logoColor=61DAFB" alt="React">
                    <div>React</div>
                    <code>https://img.shields.io/badge/React-20232A?style=for-the-badge&logo=react&logoColor=61DAFB</code>
                </div>
                <div class="badge-item">
                    <img src="https://img.shields.io/badge/Node.js-43853D?style=for-the-badge&logo=node.js&logoColor=white" alt="Node.js">
                    <div>Node.js</div>
                    <code>https://img.shields.io/badge/Node.js-43853D?style=for-the-badge&logo=node.js&logoColor=white</code>
                </div>
                <div class="badge-item">
                    <img src="https://img.shields.io/badge/Python-3776AB?style=for-the-badge&logo=python&logoColor=white" alt="Python">
                    <div>Python</div>
                    <code>https://img.shields.io/badge/Python-3776AB?style=for-the-badge&logo=python&logoColor=white</code>
                </div>
            </div>
        </div>

        <div class="badge-section">
            <h2>🎨 不同样式展示</h2>
            <div class="badge-grid">
                <div class="badge-item">
                    <img src="https://img.shields.io/badge/Style-Flat-blue?style=flat" alt="Flat Style">
                    <div>扁平化样式</div>
                    <code>style=flat</code>
                </div>
                <div class="badge-item">
                    <img src="https://img.shields.io/badge/Style-Flat_Square-blue?style=flat-square" alt="Flat Square Style">
                    <div>方形样式</div>
                    <code>style=flat-square</code>
                </div>
                <div class="badge-item">
                    <img src="https://img.shields.io/badge/Style-Plastic-blue?style=plastic" alt="Plastic Style">
                    <div>塑料样式</div>
                    <code>style=plastic</code>
                </div>
                <div class="badge-item">
                    <img src="https://img.shields.io/badge/Style-For_The_Badge-blue?style=for-the-badge" alt="For The Badge Style">
                    <div>徽章样式</div>
                    <code>style=for-the-badge</code>
                </div>
            </div>
        </div>

        <div class="usage-example">
            <h3>📝 在Markdown中使用</h3>
            <pre><code># 我的项目

![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen)
![Version](https://img.shields.io/badge/Version-v2.1.0-blue)
![License](https://img.shields.io/badge/License-MIT-yellow)

这是一个很棒的项目...</code></pre>
        </div>

        <div class="usage-example">
            <h3>🌐 在HTML中使用</h3>
            <pre><code>&lt;div class="project-badges"&gt;
  &lt;img src="https://img.shields.io/badge/Build-Passing-brightgreen" alt="Build Status"&gt;
  &lt;img src="https://img.shields.io/badge/Version-v2.1.0-blue" alt="Version"&gt;
  &lt;img src="https://img.shields.io/badge/License-MIT-yellow" alt="License"&gt;
&lt;/div&gt;</code></pre>
        </div>

        <div class="badge-section">
            <h2>💡 使用提示</h2>
            <ul>
                <li><strong>颜色选择:</strong> 使用语义化的颜色，如绿色表示成功，红色表示失败</li>
                <li><strong>文本简洁:</strong> 保持徽章文本简短明了，避免过长的描述</li>
                <li><strong>一致性:</strong> 在同一项目中保持徽章样式的一致性</li>
                <li><strong>更新及时:</strong> 确保徽章信息与实际状态保持同步</li>
                <li><strong>适度使用:</strong> 不要在README中放置过多徽章，选择最重要的信息</li>
            </ul>
        </div>

        <div class="generator-link">
            <h2>🎯 立即开始</h2>
            <p>准备好创建您自己的徽章了吗？</p>
            <a href="badge-generator.html">使用徽章生成器</a>
        </div>
    </div>
</body>
</html>
