# 🏆 徽章生成器 (Badge Generator)

一个功能完整的在线徽章生成器，帮助您轻松创建专业的项目徽章。

![Demo](https://img.shields.io/badge/Demo-Available-brightgreen)
![License](https://img.shields.io/badge/License-MIT-blue)
![HTML5](https://img.shields.io/badge/HTML5-E34F26?logo=html5&logoColor=white)
![CSS3](https://img.shields.io/badge/CSS3-1572B6?logo=css3&logoColor=white)
![JavaScript](https://img.shields.io/badge/JavaScript-F7DF1E?logo=javascript&logoColor=black)

## ✨ 功能特性

### 🎨 多样式支持
- **扁平化样式** - 现代简洁的设计
- **方形样式** - 棱角分明的外观
- **塑料样式** - 带有光泽效果的3D外观
- **徽章样式** - 大写字母的专业徽章
- **社交样式** - 适合社交媒体的圆角设计

### 🎯 自定义选项
- ✅ 自定义左右两侧文本
- ✅ 自由选择颜色（支持颜色选择器和手动输入）
- ✅ 预设颜色快速选择
- ✅ 图标支持（GitHub、NPM、Docker等）
- ✅ 可调节图标宽度
- ✅ 添加链接支持

### 🔧 实用功能
- ✅ 实时预览效果
- ✅ 自动生成标准URL参数
- ✅ 一键复制URL
- ✅ 响应式设计，支持移动设备
- ✅ 兼容Shields.io API

## 🚀 快速开始

### 在线使用
1. 打开 `badge-generator.html` 文件
2. 在左侧控制面板中设置徽章参数
3. 在右侧实时预览徽章效果
4. 复制生成的URL并使用

### 本地部署
```bash
# 克隆或下载项目文件
git clone <repository-url>

# 直接在浏览器中打开HTML文件
open badge-generator.html
```

## 📖 使用说明

### 基本设置
1. **左侧文本**: 输入徽章左侧显示的文本（如"Build"、"Version"等）
2. **右侧文本**: 输入徽章右侧显示的文本（如"Passing"、"v1.0.0"等）

### 样式选择
点击样式网格中的任意样式来切换徽章外观：
- `flat` - 扁平化
- `flat-square` - 方形
- `plastic` - 塑料（默认）
- `for-the-badge` - 徽章
- `social` - 社交

### 颜色自定义
- 使用颜色选择器快速选择颜色
- 手动输入十六进制颜色代码（如 #4c1, #e05d44）
- 点击预设颜色快速应用常用颜色

### 高级功能
- **图标**: 选择预设图标或留空
- **图标宽度**: 调整图标大小（10-50px）
- **链接**: 添加点击徽章时的跳转链接

## 🌐 生成的URL格式

生成器会创建兼容 [Shields.io](https://shields.io) 的标准URL：

```
https://img.shields.io/badge/{左侧文本}-{右侧文本}-{颜色}?{参数}
```

### 支持的参数
- `style`: 徽章样式
- `labelColor`: 左侧背景颜色
- `logo`: 图标名称
- `logoWidth`: 图标宽度
- `link`: 点击链接

### 示例URL
```
https://img.shields.io/badge/Build-Passing-brightgreen?style=flat&logo=github
```

## 📝 在项目中使用

### Markdown
```markdown
![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen)
![Version](https://img.shields.io/badge/Version-v2.1.0-blue)
```

### HTML
```html
<img src="https://img.shields.io/badge/Build-Passing-brightgreen" alt="Build Status">
<img src="https://img.shields.io/badge/Version-v2.1.0-blue" alt="Version">
```

### 在README中的典型用法
```markdown
# 我的项目

![Build](https://img.shields.io/badge/Build-Passing-brightgreen)
![Tests](https://img.shields.io/badge/Tests-95%25-green)
![Version](https://img.shields.io/badge/Version-v2.1.0-blue)
![License](https://img.shields.io/badge/License-MIT-yellow)

项目描述...
```

## 🎨 预设颜色说明

| 颜色 | 十六进制 | 用途 |
|------|----------|------|
| 🟢 成功绿 | #4c1 | 构建成功、测试通过 |
| 🔴 失败红 | #e05d44 | 构建失败、错误状态 |
| 🟡 警告黄 | #dfb317 | 警告、待处理 |
| 🔵 信息蓝 | #007ec6 | 版本、信息 |
| ⚫ 中性灰 | #9f9f9f | 标签、分类 |
| 🩷 粉红 | #ff69b4 | 特殊标记 |

## 🔧 技术实现

### 技术栈
- **HTML5**: 语义化结构
- **CSS3**: 现代样式和响应式设计
- **Vanilla JavaScript**: 无依赖的纯JS实现

### 核心功能
- **实时预览**: 监听输入变化，即时更新预览
- **URL生成**: 根据参数动态构建标准URL
- **颜色处理**: 支持多种颜色格式验证和转换
- **样式切换**: 动态应用不同的徽章样式

### 浏览器兼容性
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

## 📁 文件结构

```
badge-generator/
├── badge-generator.html    # 主要的徽章生成器
├── badge-demo.html        # 演示页面
├── README.md             # 说明文档
└── examples/             # 示例文件（可选）
```

## 🤝 贡献指南

欢迎提交问题和改进建议！

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Shields.io](https://shields.io) - 提供徽章生成服务
- [Simple Icons](https://simpleicons.org/) - 提供图标资源

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/your-username/badge-generator/issues)

---

⭐ 如果这个项目对您有帮助，请给它一个星标！
