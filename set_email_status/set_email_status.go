package main

import (
	"bytes"
	"fmt"
	"time"

	"github.com/emersion/go-imap"
	"github.com/emersion/go-imap/client"
)

type ImapConfig struct {
	Server   string
	Port     int // 使用 143 作为非加密端口
	Username string
	Password string
}

type MailMover struct {
	config ImapConfig
	client *client.Client
}

func NewMailMover(config ImapConfig) *MailMover {
	return &MailMover{
		config: config,
	}
}

// Imap连接
func (m *MailMover) Connect() error {
	fmt.Printf("Attempting to connect to %s:%d\n", m.config.Server, m.config.Port)

	c, err := client.DialTLS(fmt.Sprintf("%s:%d", m.config.Server, m.config.Port), nil)
	if err != nil {
		return fmt.Errorf("failed to connect to IMAP server: %v", err)
	}
	fmt.Println("Connection established")

	fmt.Printf("Attempting to login as %s\n", m.config.Username)
	if err := c.Login(m.config.Username, m.config.Password); err != nil {
		c.Logout()
		return fmt.Errorf("failed to login: %v", err)
	}
	fmt.Println("Login successful")

	m.client = c
	return nil
}

// 根据msgid获取邮件uid
func (m *MailMover) FindEmailByMessageID(mailbox, messageID string) ([]uint32, error) {
	_, err := m.client.Select(mailbox, true)

	if err != nil {
		return nil, err
	}

	criteria := imap.NewSearchCriteria()
	criteria.Header.Add("Message-ID", messageID)

	uids, err := m.client.Search(criteria)
	if err != nil {
		return nil, err
	}

	return uids, nil
}

// MoveEmails 移动邮件
func (m *MailMover) MoveEmails(sourceMailbox, targetMailbox string, uid uint32) error {
	if _, err := m.client.Select(sourceMailbox, false); err != nil {
		return fmt.Errorf("failed to select mailbox %s: %v", sourceMailbox, err)
	}
	seqSet := new(imap.SeqSet)
	seqSet.AddNum(uid)
	if err := m.client.Move(seqSet, targetMailbox); err != nil {
		return err
	}
	return nil
}

// 设置邮件已读未读状态
func (m *MailMover) SetReadStatus(mailbox string, uid uint32, read bool) error {
	_, err := m.client.Select(mailbox, false)
	if err != nil {
		return fmt.Errorf("failed to select mailbox: %v", err)
	}

	seqSet := new(imap.SeqSet)
	seqSet.AddNum(uid)

	if read {
		return m.client.Store(seqSet, imap.AddFlags, []interface{}{imap.SeenFlag}, nil)
	}
	return m.client.Store(seqSet, imap.RemoveFlags, []interface{}{imap.SeenFlag}, nil)

}

// 根据messageid查找邮件是否存在
func (m *MailMover) IsEmailExist(mailbox, messageID string) (bool, error) {
	_, err := m.client.Select(mailbox, true)
	if err != nil {
		return false, err
	}

	criteria := imap.NewSearchCriteria()
	criteria.Header.Add("Message-ID", messageID)

	uids, err := m.client.Search(criteria)
	if err != nil {
		return false, err
	}

	return len(uids) > 0, nil
}

// 添加邮件到本地
func (m *MailMover) AppendRawEmail(mailbox string, rawMessage []byte) error {
	// 选择邮箱
	if _, err := m.client.Select(mailbox, false); err != nil {
		return err
	}

	flags := []string{}

	err := m.client.Append(mailbox, flags, time.Now(), bytes.NewReader(rawMessage))
	if err != nil {
		return err
	}

	return nil
}

func (m *MailMover) Close() error {
	if m.client != nil {
		return m.client.Logout()
	}
	return nil
}

func main() {
	config := ImapConfig{
		Server:   "imap.mountex.net",
		Port:     993,
		Username: "<EMAIL>" + "*admin",
		Password: "shallowdream",
	}

	a := "Return-Path: <<EMAIL>>\nReceived: from TY3P286CU002.outbound.protection.outlook.com (mail-japaneastazon11020081.outbound.protection.outlook.com [*************])\n by inbound-smtp.ap-northeast-1.amazonaws.com with SMTP id 0arb4lgtt7l6iu7u218phu911fl4agle4eag8eg1\<NAME_EMAIL>;\n Mon, 02 Dec 2024 16:15:18 +0000 (UTC)\nX-SES-Spam-Verdict: PASS\nX-SES-Virus-Verdict: PASS\nReceived-SPF: pass (spfCheck: domain of hkpc.org designates ************* as permitted sender) client-ip=*************; envelope-from=<EMAIL>; helo=TY3P286CU002.outbound.protection.outlook.com;\nAuthentication-Results: amazonses.com;\n spf=pass (spfCheck: domain of hkpc.org designates ************* as permitted sender) client-ip=*************; envelope-from=<EMAIL>; helo=TY3P286CU002.outbound.protection.outlook.com;\n dkim=pass header.i=@hkpc.onmicrosoft.com;\n dmarc=pass header.from=hkpc.org;\nX-SES-RECEIPT: AEFBQUFBQUFBQUFGbnVZUG1jaGxKSnU2ZEg5T2syQytVb3BqdzI1dDlXVG03bzZVOCt6VUo4emNBbFhkTEdJdVI1R1Y1VEIya3RYYzY1Zi9udC96ZkJvQlVSUWoxWGV6Y1NvNitCclhrc1Znb1VrdzAxS2lta0o3emgwd21oWjM0UFdERTVUODVscVowOFRKQVp2QXlHMUIrZGZMaUt1ZllxR3BuUlVrQkdoNDBuNVFlOFNXU0d3NkJydXFPR1JoRTJEV2JCZzM5WXh2UFA0SUEyVVhvcS9WYzhDbDdGYTVOSHhOY0RLN2RNZk5uZml4dStlalh2OHQxcWtVeTlrU2dLVHpjQ3dBaHVvMHdrQkVQSFV5TzUySkp6c1haUzhpVnIzZlA0OHhDYXBmbWhlYllOUHEzMlE9PQ==\nX-SES-DKIM-SIGNATURE: a=rsa-sha256; q=dns/txt; b=WCPuG6vQtRpw1CvTbJ0y1o5B/a0R3JvxHBK4C3Yz+zdMBoeO4E4S1SXmcukW8Kv4mp4cUBqT9mZINQ3+GuZStgbcQmDbDY8wH7P1SPXaotfM2MBNx+my6+cLyI39/0HUy/aBWxiwWG1gswsyuFCr9zZCTJtJ2Xumau9/qN7E8NE=; c=relaxed/simple; s=wf7ez2pjvcsodozkoqksj277kza7wu47; d=amazonses.com; t=**********; v=1; bh=K4LNcgeVacqoryHAqdSc5ItuhZc5jLvSVSDcFR7K0Lw=; h=From:To:Cc:Bcc:Subject:Date:Message-ID:MIME-Version:Content-Type:X-SES-RECEIPT;\nARC-Seal: i=1; a=rsa-sha256; s=arcselector10001; d=microsoft.com; cv=none;\n b=HMKl/qlfx7swNaxkBz84Iz5Sy/KddlfgSDp91GSVwgbYwd8knZvG0oAyyVRzHgjDfKZGhmkhunjXh/WIgbe96VQHA87uLIuxquZjMfNPttNTx3IE1a49ZG+UpMh1RuRYV91VHvriNPPewG+PhDNDRqa9LD6SxBqajylHL/R9pEP4uvVrJUfYDgCB6xno8qOB3yCDZu4s3ahyABbrJMjNfNYeu2ocn8Xb45FWPufe4UJWuaxvWk8fG355jFkNUeefNgpM6yNhSiCbloTF3ozDZM7PvIG+wCACeECGMNhPJdDxKpSSjzezqr/PJLZhGvUCog/Eoet/OB6f2fi0wLbQHA==\nARC-Message-Signature: i=1; a=rsa-sha256; c=relaxed/relaxed; d=microsoft.com;\n s=arcselector10001;\n h=From:Date:Subject:Message-ID:Content-Type:MIME-Version:X-MS-Exchange-AntiSpam-MessageData-ChunkCount:X-MS-Exchange-AntiSpam-MessageData-0:X-MS-Exchange-AntiSpam-MessageData-1;\n bh=K4LNcgeVacqoryHAqdSc5ItuhZc5jLvSVSDcFR7K0Lw=;\n b=L/GA09j0u3u09lVazFXMNH+OKlsVweXYUFAqFzBEFMLl8loMJ8S3kAednsPdByowD0lyq3FItcr78sR8hZLIfmlSpy5iWvH6IiDDg5aFEh2QSF5eQnmCsccHjfDwPLXy7kUHXODkgitw0CIwaIxZsijQy0ElcfFgbvrUBkEZT73cB2lDYbhTgkE8RUlYWiJpyCNuk7/TVZ8+5t+E7US6hit7SZzsUMITDPJ/G+hsySZEoJsccPRZXQ2VZkPL1Yv60Ig/tXJqzr6kv1yaKYApegUS6hLRoW0QFppydt2fzqtpTZqBxDlzuvDDRtvl+VmjBPKBRssbgJthGLxGC8du5g==\nARC-Authentication-Results: i=1; mx.microsoft.com 1; spf=pass\n smtp.mailfrom=hkpc.org; dmarc=pass action=none header.from=hkpc.org;\n dkim=pass header.d=hkpc.org; arc=none\nDKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed; d=hkpc.onmicrosoft.com;\n s=selector2-hkpc-onmicrosoft-com;\n h=From:Date:Subject:Message-ID:Content-Type:MIME-Version:X-MS-Exchange-SenderADCheck;\n bh=K4LNcgeVacqoryHAqdSc5ItuhZc5jLvSVSDcFR7K0Lw=;\n b=jLrmbLNTWLhL02+cJQTO7oCp410FAmacT0zir9Bp7q4TbwjMTu2oBHCYlq2VGYQ0zw9jUchAdcgRjY0VlIhLfso2RGXV2Pywy07niB8H5aWJXeZ5BiR72Fe690ROHQ+HE1vcBLJ06/Uu2FAReedtRKiYpLNd3VadX01Z4A9vS3M=\nAuthentication-Results: dkim=none (message not signed)\n header.d=none;dmarc=none action=none header.from=hkpc.org;\nReceived: from TYCP286MB3738.JPNP286.PROD.OUTLOOK.COM (2603:1096:400:389::6)\n by TYRP286MB4247.JPNP286.PROD.OUTLOOK.COM (2603:1096:405:130::10) with\n Microsoft SMTP Server (version=TLS1_2,\n cipher=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384) id 15.20.8207.18; Mon, 2 Dec\n 2024 16:15:16 +0000\nReceived: from TYCP286MB3738.JPNP286.PROD.OUTLOOK.COM\n ([fe80::ae5a:4ebc:5ef8:b2ff]) by TYCP286MB3738.JPNP286.PROD.OUTLOOK.COM\n ([fe80::ae5a:4ebc:5ef8:b2ff%4]) with mapi id 15.20.8207.017; Mon, 2 Dec 2024\n 16:15:16 +0000\nFrom: \"TVP ePROQ system\" <<EMAIL>>\nTo: \"Hung Huen Shing\" <<EMAIL>>\nDate: 3 Dec 2024 00:15:16 +0800\nSubject: =?utf-8?B?Tm90aWZpY2F0aW9uIGFuZCBJbnZpdGF0aW9uIG9mIFJlcXVlc3QgZm9yIFF1b3RhdGlvbiAoUkZRKSAoUkZRMjAyNDExLTE0NTMgLSBEZXZlbG9wIENvdXJzZSBtYW5hZ2VtZW50IHN5c3RlbSB3aXRowqBBbmRyb2lkwqBhcHBzKQ==?=\nContent-Type: text/html; charset=utf-8\nContent-Transfer-Encoding: base64\nX-ClientProxiedBy: SG2PR02CA0053.apcprd02.prod.outlook.com\n (2603:1096:4:54::17) To TYCP286MB3738.JPNP286.PROD.OUTLOOK.COM\n (2603:1096:400:389::6)\nReturn-Path: <EMAIL>\nMessage-ID:\n <<EMAIL>>\nMIME-Version: 1.0\nX-MS-PublicTrafficType: Email\nX-MS-TrafficTypeDiagnostic: TYCP286MB3738:EE_|TYRP286MB4247:EE_\nX-MS-Office365-Filtering-Correlation-Id: 8705cc15-aea9-4d6d-be9d-08dd12ec8263\nX-MS-Exchange-SenderADCheck: 1\nX-MS-Exchange-AntiSpam-Relay: 0\nX-Microsoft-Antispam:\n\tBCL:0;ARA:13230040|366016|1800799024|52116014|376014|8096899003|38350700014;\nX-Microsoft-Antispam-Message-Info:\n\t=?utf-8?B?T0FaaUh0TGlUTzdmRElCUVhzQ052bWV3M2FSb3kxMElmK1hkbHE0cnArWnNR?=\n =?utf-8?B?Y0FQVzhYbitXUFY0YmNrNzk5VVBSLzZ3N0txQVFSUkdkWDdxbDJFRStVT2Ns?=\n =?utf-8?B?YzFaWlZmSE42cWFZT2NsWWw0QnQxT2lDYnIxV1ladzFkTERidGpyYWlJek85?=\n =?utf-8?B?SlVFd1dDbzJ0czU0MktXcjlLaVNCVURFUC9nWC9pL3pud3pZbWp0aVdjMHFF?=\n =?utf-8?B?MUpVcWtFWUpPQjU5TnZOOE5JQ0pOSjBCUDBDRDd1ams0NkNQT2hRQmR1TkxL?=\n =?utf-8?B?QWRMeHhhd1NEK01QelRRdVZwVVhYa2hqVTdsb0c5YVQvdVdFYXZPVUFGVlJq?=\n =?utf-8?B?bkZxdjBqSHlHODFEN3lLVTVlNzNpSWVmWlk4RG5rSDFiMzJQTGpSNzRpWk9j?=\n =?utf-8?B?UGpkTHZQNlFiNmE4Wk5wM1Q1NWVSL3AyNndjMGZpalZaTHhiclJnSitMYUpD?=\n =?utf-8?B?T2tPL2hBZlR0UURCZTFqYnYzMDlTU3VVREJIaVhCL3ZwVWp4empYZ1Nld3d5?=\n =?utf-8?B?MTN3VzVjbU1lLzZWWDNlRG9xeVg1QzNlUWJFVlJ0dWIxTjBXM25ta1ArejNQ?=\n =?utf-8?B?NUJwMW1nWmJpTkN5b0UvT3hTditmUkYxNGVWU0tHbU5RVDBJa2xpa2dNUXRB?=\n =?utf-8?B?ZjkxcUs5bzdIS2lQYXN3eE9vZ2hSUUdVeVpqa1B3MlNDRTc4UE5oK0hwTFUv?=\n =?utf-8?B?TDJFcjM5S0p6MSs4NHJOL2k0Z0hSTHpTekEwckdiUTBUM0M0NWZWSThlMnFV?=\n =?utf-8?B?OVVFUUJMbTV6SXdWK2J1ZmRaRnVXL0xGenhSeGU5NS9ldEVCK3JGUmJNdmZV?=\n =?utf-8?B?Vnowd1lvYkRwRFVud1ZFMjRucG5QQVN2ZFB6SDV3QzQ5b1F5VjFSZFVreXBJ?=\n =?utf-8?B?SmpzemxCWUtCcmQwbTdST0NDVlNIN0xwTW9YU3IyRDJBNXQ3R2FrVG1mS0FY?=\n =?utf-8?B?bWFLNjFYM09ydk5QRVJpU3REL214OEo0V0NDSVlQSGQwNWxpWkRnT1lTdzFB?=\n =?utf-8?B?djdsOTY5NlRERmZTNFZVMzd3SjJjcVlDNzhvWmprMityUHZSRCtBRXV1TlRL?=\n =?utf-8?B?NTdBckpDM3RPTnJ2MEJOTEdyMmsvM0g3aXhsK3BwQ2xiVGFyU0tZaFAva1kz?=\n =?utf-8?B?QjkvWThSWVViN090WnI1aTRMS3FQb0pkNmxTRytVVGx2SURUK0lGMWY5SGpW?=\n =?utf-8?B?TFVXaXVhM1lGMDRUQVlkOWJhUUdsSURTYlpOeVhjWlJCQzEzM3hxZFVVclFE?=\n =?utf-8?B?QTVyZGhtbWo1TWtBK01MVENMaVA3aUVYeHhLaWVzS2ljN2NTVHFjcEdPU3Fk?=\n =?utf-8?B?bWFtTTJEWERtRjZzVGJ3V05xVmpyQXVvNWg3cnBaYUdPSmdyWHR5QTlFMzNu?=\n =?utf-8?B?UnJnRkw5WDEzSnNkNjVDbkxOKzRHUUFRQ25FZUhRalJsWENTOEpVRnpEWGhP?=\n =?utf-8?B?WGVNVzZ6K2JxaTVpd3plMGo4OC9BSTUxTVBCR09PaEFhRG9KQXNSMXVNQ3pw?=\n =?utf-8?B?OU1tcEZsNlNyQm1EUnBVZjJxcE92Z09ZK3ZCNGFXdXZCMENUck1YaUFjSWVh?=\n =?utf-8?B?c1FxNHgzN0xSbFR1cXVwejh6bnFEOWdPOHVrTldycFdJR0xkWlo2RUtGa0Q4?=\n =?utf-8?B?dFN2ZGlTSjVyR0ZFbyt5SitaKzZMdzZKUjRTUktYdzU3RHFNMmt2UnJzaVFn?=\n =?utf-8?B?aVZ0d2V5RTJidEZJRHZzS2pGMmRrKzBmNGlibk5kblJ6K1ZXTWJ4K3FTZEls?=\n =?utf-8?B?elhSUU53d2Zka3EvY05lOERCdExtWGU2c1JtSTRIQ2grOEVNVkxmeUl4UzRC?=\n =?utf-8?Q?R5DABY8xqywRhulChBmT31k2z30d8wpbqo+mk=3D?=\nX-Forefront-Antispam-Report:\n\tCIP:***************;CTRY:;LANG:zh-tw;SCL:1;SRV:;IPV:NLI;SFV:NSPM;H:TYCP286MB3738.JPNP286.PROD.OUTLOOK.COM;PTR:;CAT:NONE;SFS:(13230040)(366016)(1800799024)(52116014)(376014)(8096899003)(38350700014);DIR:OUT;SFP:1102;\nX-MS-Exchange-AntiSpam-MessageData-ChunkCount: 1\nX-MS-Exchange-AntiSpam-MessageData-0:\n\t=?utf-8?B?LzRZM054eGdXa3RqK3pOdmcyZS91M1ZmbzZhaGdVVFRmTlVIT2lkM0IrZXpD?=\n =?utf-8?B?ajJiQS8rS2VxaEFRbnNEc2htQmZSYUozdXBzRzdlZ2dQN0xYVS8rdTBlTk1r?=\n =?utf-8?B?d3RmNzdYVXhGQVNxZUhjUVoremlDeVlNTFQ5NVFYaVN0dVlndmtDZjZFMU5I?=\n =?utf-8?B?eGN3eURKaU92SyttODh2TSs5OUtEZFY5bVppbGNmWldYUTdxWndQdmJYcSsv?=\n =?utf-8?B?TGpobjlPL3RwZkpCWnM0Q3J1cUZIMGNIUWkxcnNSeWxWYVhSbFIvTCt1UGcx?=\n =?utf-8?B?VktRazNHK2NoZGtydjh0WU1KeHBJM0FHcFMzRkNJRG1tUURJemNYelAxQUJj?=\n =?utf-8?B?VHloaHRRamhpTWZFYitadTd2R1Vkb1RsNDhtYkgrYnJLeEhBMHZzcmJaSnd3?=\n =?utf-8?B?VU9YOGlZRVZWMTBQS1ZRd3o1cUlPWkhtU3A3ejZZdERnZVlwOFVpUGN6eG9R?=\n =?utf-8?B?RDhXVXJXWWtYWDVZRkhRd1JXbTF6cEN6ZmlZMTFGbCt3UURPbTIwcGcyak1C?=\n =?utf-8?B?Ty9iMFdEbDUvYzBVTkVZSTBsd3QwOFRoMXhKa1BJc2paREs5anY0U2hqYnVQ?=\n =?utf-8?B?WEl2YnUzZ3Y0OXF5N29QcTYweTVEL1VreGRtUy9wWlBUaFd1QWtGNzBFVDdw?=\n =?utf-8?B?SGdVWVJwMkt5aUQwdXQrS3VMMVRGMm9zTGpUd1VORWJxZUVaTE12dSt1YzV1?=\n =?utf-8?B?M3BHT1dqVGV3ZmVuQStSMEx6K3JvVE8vMlFyemQ3OHJ2MXJSb2J0R3U5MEFP?=\n =?utf-8?B?cmlpVzU1RGRrbUtadS81K0loTFpGSXEvSThGMTRjMVZqWERJZHBla0pYQVRF?=\n =?utf-8?B?dHNLbmtSaUVLUkg1dmF1YktoYnZpdGNsOWJwY28ydTJJUm5hQlc0L21YalJh?=\n =?utf-8?B?TEJjZWZpK05jcWVrdkVQaTVhZzJvN2JraVpCNXBvTzkxR0dKMHpNVlN4ZTZS?=\n =?utf-8?B?Tk80WEpHSC9UNko2aGtHM2daaTdQZ1NNOWo4bEpqWjgzcEhoZ2V6c3A0SU5E?=\n =?utf-8?B?VmhvbzdqSHJhUjN4dldEM2dQeE8zMm1pLzFScWlqTGU4V0FqcWZYakN0YURR?=\n =?utf-8?B?eE1IREZJbVlBdEJ5ZkRJRm9xd3BTRWcvaE1HdFlISEJ4SXVsQWxDOFN2Y3ZT?=\n =?utf-8?B?Qml0WEpPZG9NSHA3Y0JpajAzS3FydTIxMzFCbEUzZWxnNHE4WjhXeUczaCt3?=\n =?utf-8?B?M3Q3RnZrSFAzL2NxWC8vU0JVWktodTVZdHRXdXFUZnVwZFhkdGlDdmFGWStq?=\n =?utf-8?B?bXlOekhMb1NBVTZXSnh1T05EaXJUc1hRREhPcU1xUmJNVmlXVlFaR0xrL1ZN?=\n =?utf-8?B?UFBsandPd0s1d2ZtUTZyc3AxR0E0aURKMkdPeFN1akxZM2dDUnhPS1FEckNG?=\n =?utf-8?B?am1Wd0crcytKZzNxR0QyNEMrazlVVVQxa3dIS24wb0NQSlhQanVxUEd4bEwx?=\n =?utf-8?B?dEFYZVI1QWpudmhDNklibTNTY2o5dTVkMnQrU1E3eUZlQTZLdDVRWGxlU0JH?=\n =?utf-8?B?WWc0Uk5NeGZQeFRETGFsM1gwS2wzTGFhODNBd1pFdXFxbGtCWEVXRldka1Na?=\n =?utf-8?B?ZnFCSE90dkJTYUNaQUlEWFVNNFlIV0cxZ0UranR4bnpZdGNLSzUyVWVpM093?=\n =?utf-8?B?UnZTNk5aZnQ0TnI0ejVaZ0JNaHlSNnVNNUxJd1crQjU0KzBmSmxoTk1FRm9h?=\n =?utf-8?B?QlFBdjJwMlhyaUl0WGVLMjdTcTZhOFFBbmZ5NEY1a2hCSlUzZWFoVzBXUHdn?=\n =?utf-8?B?Z1ZGcUVENTlXTkZVU0RuTmtFbXhZcWFUbHN0MzYxakZYWDhxUmUyTWFMUHFT?=\n =?utf-8?B?bTdDRHN0VmZoTTlYZUcza045YTdxZVF6QTg2SE5ZWXlGUTV3dmFaQzhaUU9z?=\n =?utf-8?B?UUY0WFNkWGNyQkJNc0tia3ZTWmdZdlJUbUhLclhJY1AyS1Qxbyt1VU9jTFpV?=\n =?utf-8?B?ZU9keUlJZlYxNXFuSTdTUG9GSCtjdlBPNURQbURSWkZNSXlXMDI4c25yYXRv?=\n =?utf-8?B?NmZUSXpTZVN1QXVjQ3hvTW5jVmJScVJkTUNVUjUvQklYWUJhcDFEa3QxRDdW?=\n =?utf-8?B?MTdDN1BXekFDSTdRRU40K2tmSTNnaVViYzQyenB5cTdrbGFpMXJuM0l5emFz?=\n =?utf-8?Q?HAtqwELJ4tserUKCxbn1AaCpN?=\nX-OriginatorOrg: hkpc.org\nX-MS-Exchange-CrossTenant-Network-Message-Id: 8705cc15-aea9-4d6d-be9d-08dd12ec8263\nX-MS-Exchange-CrossTenant-AuthSource: TYCP286MB3738.JPNP286.PROD.OUTLOOK.COM\nX-MS-Exchange-CrossTenant-AuthAs: Internal\nX-MS-Exchange-CrossTenant-OriginalArrivalTime: 02 Dec 2024 16:15:16.7038\n (UTC)\nX-MS-Exchange-CrossTenant-FromEntityHeader: Hosted\nX-MS-Exchange-CrossTenant-Id: 07373b9f-47dd-4621-ad2c-e5bbfc8863f2\nX-MS-Exchange-CrossTenant-MailboxType: HOSTED\nX-MS-Exchange-CrossTenant-UserPrincipalName: mP42/0HjDEwY4GxG2fyMqkS5Nc4c1Gs9HaMBv9L/XIbQcygtQtg3+tBaSAK3C/gTN9VCwgrBTrxtKblCeRuF1g==\nX-MS-Exchange-Transport-CrossTenantHeadersStamped: TYRP286MB4247\n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\nL2JvZHk+DQo8L2h0bWw+DQo=\n"

	mover := NewMailMover(config)
	if err := mover.Connect(); err != nil {
		fmt.Println(fmt.Sprintf("连接 Imap 服务器失败，原因：%s", err))
	}
	defer mover.Close()

	err := mover.AppendRawEmail("Junk", []byte(a))
	fmt.Println(err)

}
