# permissions.py - 整数权限枚举

class PermissionEnum:
    """
    权限编码枚举 - 使用整数常量
    每个权限对应一个唯一整数ID
    """

    # 仪表板权限 (1-99)
    DASHBOARD_VIEW = 1

    # 房务管理权限 (100-199)
    ROOM_STATUS_VIEW = 100
    ROOM_STATUS_EDIT = 101

    ROOM_CHECKIN_VIEW = 110
    ROOM_CHECKIN_CREATE = 111
    ROOM_CHECKIN_EDIT = 112

    ROOM_CHECKOUT_VIEW = 120
    ROOM_CHECKOUT_PROCESS = 121

    ROOM_INFO_VIEW = 130
    ROOM_INFO_EDIT = 131
    ROOM_INFO_DELETE = 132

    ROOM_CHANGE_VIEW = 140
    ROOM_CHANGE_PROCESS = 141

    # 人员与排班管理 (200-299)
    STAFF_INFO_VIEW = 200
    STAFF_INFO_EDIT = 201

    STAFF_SCHEDULE_VIEW = 210
    STAFF_SCHEDULE_EDIT = 211

    # 母婴核心记录 (300-399)
    NURSING_RECORD_VIEW = 300
    NURSING_RECORD_ADD = 301
    NURSING_RECORD_EDIT = 302
    NURSING_RECORD_DELETE = 303
    NURSING_RECORD_EXPORT = 304

    # 系统管理 (400-499)
    SYSTEM_USER_VIEW = 400
    SYSTEM_USER_CREATE = 401
    SYSTEM_USER_EDIT = 402
    SYSTEM_USER_DELETE = 403

    SYSTEM_ROLE_VIEW = 410
    SYSTEM_ROLE_CREATE = 411
    SYSTEM_ROLE_EDIT = 412
    SYSTEM_ROLE_DELETE = 413

    SYSTEM_LOG_VIEW = 420

    SYSTEM_HIS_VIEW = 430
    SYSTEM_HIS_EDIT = 431

    # 权限选择列表 - 用于Django表单和模型
    PERMISSION_CHOICES = [
        # 仪表板
        (DASHBOARD_VIEW, "查看仪表板"),

        # 房务管理
        (ROOM_STATUS_VIEW, "查看房态总览"),
        (ROOM_STATUS_EDIT, "编辑房态信息"),

        (ROOM_CHECKIN_VIEW, "查看入住管理"),
        (ROOM_CHECKIN_CREATE, "创建入住记录"),
        (ROOM_CHECKIN_EDIT, "编辑入住记录"),

        (ROOM_CHECKOUT_VIEW, "查看退房管理"),
        (ROOM_CHECKOUT_PROCESS, "办理退房"),

        (ROOM_INFO_VIEW, "查看房间信息"),
        (ROOM_INFO_EDIT, "编辑房间信息"),
        (ROOM_INFO_DELETE, "删除房间信息"),

        (ROOM_CHANGE_VIEW, "查看换房管理"),
        (ROOM_CHANGE_PROCESS, "办理换房"),

        # 人员与排班
        (STAFF_INFO_VIEW, "查看员工信息"),
        (STAFF_INFO_EDIT, "编辑员工信息"),

        (STAFF_SCHEDULE_VIEW, "查看排班信息"),
        (STAFF_SCHEDULE_EDIT, "编辑排班信息"),

        # 母婴核心记录
        (NURSING_RECORD_VIEW, "查看护理记录"),
        (NURSING_RECORD_ADD, "新增护理记录"),
        (NURSING_RECORD_EDIT, "编辑护理记录"),
        (NURSING_RECORD_DELETE, "删除护理记录"),
        (NURSING_RECORD_EXPORT, "导出护理记录"),

        # 系统管理
        (SYSTEM_USER_VIEW, "查看用户"),
        (SYSTEM_USER_CREATE, "创建用户"),
        (SYSTEM_USER_EDIT, "编辑用户"),
        (SYSTEM_USER_DELETE, "删除用户"),

        (SYSTEM_ROLE_VIEW, "查看角色"),
        (SYSTEM_ROLE_CREATE, "创建角色"),
        (SYSTEM_ROLE_EDIT, "编辑角色"),
        (SYSTEM_ROLE_DELETE, "删除角色"),

        (SYSTEM_LOG_VIEW, "查看审计日志"),

        (SYSTEM_HIS_VIEW, "查看HIS配置"),
        (SYSTEM_HIS_EDIT, "编辑HIS配置"),
    ]

    # 权限分类 - 用于UI组织展示
    PERMISSION_CATEGORIES = {
        "仪表板": [DASHBOARD_VIEW],
        "房态管理": [ROOM_STATUS_VIEW, ROOM_STATUS_EDIT],
        "入住管理": [ROOM_CHECKIN_VIEW, ROOM_CHECKIN_CREATE, ROOM_CHECKIN_EDIT],
        "退房管理": [ROOM_CHECKOUT_VIEW, ROOM_CHECKOUT_PROCESS],
        "房间信息": [ROOM_INFO_VIEW, ROOM_INFO_EDIT, ROOM_INFO_DELETE],
        "换房管理": [ROOM_CHANGE_VIEW, ROOM_CHANGE_PROCESS],
        "员工管理": [STAFF_INFO_VIEW, STAFF_INFO_EDIT],
        "排班管理": [STAFF_SCHEDULE_VIEW, STAFF_SCHEDULE_EDIT],
        "护理记录": [NURSING_RECORD_VIEW, NURSING_RECORD_ADD, NURSING_RECORD_EDIT,
                     NURSING_RECORD_DELETE, NURSING_RECORD_EXPORT],
        "用户管理": [SYSTEM_USER_VIEW, SYSTEM_USER_CREATE, SYSTEM_USER_EDIT, SYSTEM_USER_DELETE],
        "角色管理": [SYSTEM_ROLE_VIEW, SYSTEM_ROLE_CREATE, SYSTEM_ROLE_EDIT, SYSTEM_ROLE_DELETE],
        "系统日志": [SYSTEM_LOG_VIEW],
        "HIS配置": [SYSTEM_HIS_VIEW, SYSTEM_HIS_EDIT],
    }

    @classmethod
    def get_permission_name(cls, permission_id):
        """根据权限ID获取权限名称"""
        for id, name in cls.PERMISSION_CHOICES:
            if id == permission_id:
                return name
        return None

    @classmethod
    def get_all_permissions(cls):
        """获取所有权限ID列表"""
        return [id for id, _ in cls.PERMISSION_CHOICES]

    @classmethod
    def get_module_permissions(cls, module_name):
        """获取指定模块的所有权限"""
        return cls.PERMISSION_CATEGORIES.get(module_name, [])


a = PermissionEnum()
print(a.get_module_permissions('用户管理'))