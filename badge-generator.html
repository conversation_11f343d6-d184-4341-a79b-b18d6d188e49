<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>徽章生成器 - Badge Generator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0;
            min-height: 600px;
        }

        .control-panel {
            padding: 30px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
        }

        .preview-panel {
            padding: 30px;
            background: white;
        }

        .section {
            margin-bottom: 25px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #4facfe;
        }

        .style-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 10px;
        }

        .style-option {
            text-align: center;
            cursor: pointer;
            padding: 10px;
            border-radius: 8px;
            transition: all 0.3s;
            border: 2px solid transparent;
        }

        .style-option:hover {
            background: #f0f0f0;
        }

        .style-option.active {
            border-color: #4facfe;
            background: #e3f2fd;
        }

        .style-preview {
            height: 30px;
            margin-bottom: 5px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: white;
            font-weight: bold;
        }

        .flat-style { background: linear-gradient(90deg, #555 50%, #4c1 50%); }
        .flat-square-style { background: linear-gradient(90deg, #555 50%, #4c1 50%); border-radius: 0; }
        .plastic-style { 
            background: linear-gradient(90deg, #555 50%, #4c1 50%);
            box-shadow: inset 0 1px 0 rgba(255,255,255,0.2);
        }
        .badge-style { 
            background: linear-gradient(90deg, #555 50%, #4c1 50%);
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .social-style { 
            background: linear-gradient(90deg, #555 50%, #4c1 50%);
            border-radius: 3px;
        }

        .color-input-group {
            display: flex;
            gap: 10px;
        }

        .color-input-group input[type="color"] {
            width: 50px;
            height: 40px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
        }

        .color-input-group input[type="text"] {
            flex: 1;
        }

        .color-presets {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .preset-color {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            transition: transform 0.2s;
        }

        .preset-color:hover {
            transform: scale(1.1);
        }

        .preview-container {
            text-align: center;
            padding: 40px;
            background: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 25px;
        }

        .badge-preview {
            display: inline-block;
            font-family: 'Helvetica Neue', Arial, sans-serif;
            font-size: 11px;
            font-weight: bold;
            line-height: 1;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .badge-left, .badge-right {
            display: inline-block;
            padding: 6px 8px;
            vertical-align: middle;
        }

        .badge-left {
            background: #555;
            color: white;
        }

        .badge-right {
            background: #4c1;
            color: white;
        }

        .url-container {
            position: relative;
        }

        .url-container textarea {
            width: 100%;
            height: 100px;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            resize: vertical;
        }

        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #4facfe;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }

        .copy-btn:hover {
            background: #3d8bfe;
        }

        .usage-content {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            font-size: 14px;
            line-height: 1.6;
        }

        .usage-content code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .control-panel {
                border-right: none;
                border-bottom: 1px solid #e9ecef;
            }
            
            header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🏆 徽章生成器</h1>
            <p>创建自定义徽章，生成URL参数，轻松集成到您的项目中</p>
        </header>

        <main class="main-content">
            <!-- 控制面板 -->
            <div class="control-panel">
                <h2>徽章设置</h2>
                
                <!-- 基本设置 -->
                <div class="section">
                    <h3>基本信息</h3>
                    <div class="form-group">
                        <label for="leftText">左侧文本:</label>
                        <input type="text" id="leftText" value="Status" placeholder="例如: Build">
                    </div>
                    <div class="form-group">
                        <label for="rightText">右侧文本:</label>
                        <input type="text" id="rightText" value="Passing" placeholder="例如: Success">
                    </div>
                </div>

                <!-- 样式选择 -->
                <div class="section">
                    <h3>徽章样式</h3>
                    <div class="style-grid">
                        <div class="style-option" data-style="flat">
                            <div class="style-preview flat-style">扁平</div>
                            <span>扁平化</span>
                        </div>
                        <div class="style-option" data-style="flat-square">
                            <div class="style-preview flat-square-style">方形</div>
                            <span>方形</span>
                        </div>
                        <div class="style-option active" data-style="plastic">
                            <div class="style-preview plastic-style">塑料</div>
                            <span>塑料</span>
                        </div>
                        <div class="style-option" data-style="for-the-badge">
                            <div class="style-preview badge-style">徽章</div>
                            <span>徽章</span>
                        </div>
                        <div class="style-option" data-style="social">
                            <div class="style-preview social-style">社交</div>
                            <span>社交</span>
                        </div>
                    </div>
                </div>

                <!-- 颜色设置 -->
                <div class="section">
                    <h3>颜色设置</h3>
                    <div class="color-group">
                        <div class="form-group">
                            <label for="leftColor">左侧颜色:</label>
                            <div class="color-input-group">
                                <input type="color" id="leftColor" value="#555555">
                                <input type="text" id="leftColorText" value="#555555" placeholder="#555555">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="rightColor">右侧颜色:</label>
                            <div class="color-input-group">
                                <input type="color" id="rightColor" value="#4c1">
                                <input type="text" id="rightColorText" value="#4c1" placeholder="#4c1">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 预设颜色 -->
                    <div class="preset-colors">
                        <h4>预设颜色:</h4>
                        <div class="color-presets">
                            <div class="preset-color" data-color="#4c1" style="background: #4c1" title="成功绿"></div>
                            <div class="preset-color" data-color="#e05d44" style="background: #e05d44" title="失败红"></div>
                            <div class="preset-color" data-color="#dfb317" style="background: #dfb317" title="警告黄"></div>
                            <div class="preset-color" data-color="#007ec6" style="background: #007ec6" title="信息蓝"></div>
                            <div class="preset-color" data-color="#9f9f9f" style="background: #9f9f9f" title="中性灰"></div>
                            <div class="preset-color" data-color="#ff69b4" style="background: #ff69b4" title="粉红"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 预览和输出 -->
            <div class="preview-panel">
                <h2>实时预览</h2>

                <!-- 徽章预览 -->
                <div class="preview-container">
                    <div id="badgePreview" class="badge-preview">
                        <span class="badge-left">Status</span><span class="badge-right">Passing</span>
                    </div>
                </div>

                <!-- 高级设置 -->
                <div class="section">
                    <h3>高级设置</h3>
                    <div class="form-group">
                        <label for="logo">图标 (可选):</label>
                        <select id="logo">
                            <option value="">无图标</option>
                            <option value="github">GitHub</option>
                            <option value="npm">NPM</option>
                            <option value="docker">Docker</option>
                            <option value="javascript">JavaScript</option>
                            <option value="python">Python</option>
                            <option value="react">React</option>
                            <option value="vue">Vue</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="logoWidth">图标宽度:</label>
                        <input type="number" id="logoWidth" value="13" min="10" max="50">
                    </div>
                    <div class="form-group">
                        <label for="link">链接 (可选):</label>
                        <input type="url" id="link" placeholder="https://example.com">
                    </div>
                </div>

                <!-- URL生成 -->
                <div class="section">
                    <h3>生成的URL</h3>
                    <div class="url-container">
                        <textarea id="generatedUrl" readonly placeholder="生成的URL将显示在这里..."></textarea>
                        <button id="copyUrl" class="copy-btn">📋 复制URL</button>
                    </div>
                </div>

                <!-- 使用说明 -->
                <div class="section">
                    <h3>使用说明</h3>
                    <div class="usage-content">
                        <p><strong>如何使用生成的URL:</strong></p>
                        <ol>
                            <li>复制上方生成的URL</li>
                            <li>在浏览器中访问该URL即可获得徽章图片</li>
                            <li>可以在Markdown中使用: <code>![Badge](URL)</code></li>
                            <li>可以在HTML中使用: <code>&lt;img src="URL" alt="Badge"&gt;</code></li>
                        </ol>

                        <p><strong>支持的格式:</strong></p>
                        <ul>
                            <li>PNG格式 (默认)</li>
                            <li>SVG格式 (在URL末尾添加 .svg)</li>
                        </ul>

                        <p><strong>示例服务:</strong></p>
                        <ul>
                            <li><a href="https://shields.io" target="_blank">Shields.io</a> - 最流行的徽章服务</li>
                            <li><a href="https://badgen.net" target="_blank">Badgen.net</a> - 快速徽章生成</li>
                        </ul>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 徽章生成器主要功能
        class BadgeGenerator {
            constructor() {
                this.initializeElements();
                this.bindEvents();
                this.updatePreview();
            }

            initializeElements() {
                // 获取所有需要的DOM元素
                this.leftText = document.getElementById('leftText');
                this.rightText = document.getElementById('rightText');
                this.leftColor = document.getElementById('leftColor');
                this.rightColor = document.getElementById('rightColor');
                this.leftColorText = document.getElementById('leftColorText');
                this.rightColorText = document.getElementById('rightColorText');
                this.logo = document.getElementById('logo');
                this.logoWidth = document.getElementById('logoWidth');
                this.link = document.getElementById('link');
                this.badgePreview = document.getElementById('badgePreview');
                this.generatedUrl = document.getElementById('generatedUrl');
                this.copyBtn = document.getElementById('copyUrl');
                this.styleOptions = document.querySelectorAll('.style-option');
                this.presetColors = document.querySelectorAll('.preset-color');

                this.currentStyle = 'plastic';
            }

            bindEvents() {
                // 绑定所有事件监听器
                this.leftText.addEventListener('input', () => this.updatePreview());
                this.rightText.addEventListener('input', () => this.updatePreview());

                // 颜色选择器事件
                this.leftColor.addEventListener('change', (e) => {
                    this.leftColorText.value = e.target.value;
                    this.updatePreview();
                });
                this.rightColor.addEventListener('change', (e) => {
                    this.rightColorText.value = e.target.value;
                    this.updatePreview();
                });

                // 颜色文本输入事件
                this.leftColorText.addEventListener('input', (e) => {
                    if (this.isValidColor(e.target.value)) {
                        this.leftColor.value = e.target.value;
                        this.updatePreview();
                    }
                });
                this.rightColorText.addEventListener('input', (e) => {
                    if (this.isValidColor(e.target.value)) {
                        this.rightColor.value = e.target.value;
                        this.updatePreview();
                    }
                });

                // 样式选择事件
                this.styleOptions.forEach(option => {
                    option.addEventListener('click', () => {
                        this.styleOptions.forEach(opt => opt.classList.remove('active'));
                        option.classList.add('active');
                        this.currentStyle = option.dataset.style;
                        this.updatePreview();
                    });
                });

                // 预设颜色事件
                this.presetColors.forEach(preset => {
                    preset.addEventListener('click', () => {
                        const color = preset.dataset.color;
                        this.rightColor.value = color;
                        this.rightColorText.value = color;
                        this.updatePreview();
                    });
                });

                // 高级设置事件
                this.logo.addEventListener('change', () => this.updatePreview());
                this.logoWidth.addEventListener('input', () => this.updatePreview());
                this.link.addEventListener('input', () => this.updatePreview());

                // 复制URL事件
                this.copyBtn.addEventListener('click', () => this.copyToClipboard());
            }

            isValidColor(color) {
                // 验证颜色格式 (#hex 或 颜色名称)
                const hexPattern = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
                const namedColors = ['red', 'green', 'blue', 'yellow', 'orange', 'purple', 'pink', 'brown', 'black', 'white', 'gray', 'grey'];
                return hexPattern.test(color) || namedColors.includes(color.toLowerCase());
            }

            updatePreview() {
                // 更新徽章预览
                const leftText = this.leftText.value || 'Status';
                const rightText = this.rightText.value || 'Passing';
                const leftColor = this.leftColorText.value || '#555555';
                const rightColor = this.rightColorText.value || '#4c1';

                // 更新预览样式
                this.badgePreview.innerHTML = `
                    <span class="badge-left" style="background-color: ${leftColor}">${leftText}</span><span class="badge-right" style="background-color: ${rightColor}">${rightText}</span>
                `;

                // 应用样式类
                this.badgePreview.className = `badge-preview ${this.currentStyle}-preview`;

                // 生成URL
                this.generateUrl();
            }

            generateUrl() {
                // 生成shields.io兼容的URL
                const leftText = encodeURIComponent(this.leftText.value || 'Status');
                const rightText = encodeURIComponent(this.rightText.value || 'Passing');
                const leftColor = this.leftColorText.value.replace('#', '') || '555555';
                const rightColor = this.rightColorText.value.replace('#', '') || '4c1';
                const style = this.currentStyle;
                const logo = this.logo.value;
                const logoWidth = this.logoWidth.value;
                const link = this.link.value;

                // 构建基础URL
                let url = `https://img.shields.io/badge/${leftText}-${rightText}-${rightColor}`;

                // 添加参数
                const params = [];
                if (style !== 'plastic') params.push(`style=${style}`);
                if (leftColor !== '555555') params.push(`labelColor=${leftColor}`);
                if (logo) params.push(`logo=${logo}`);
                if (logoWidth !== '13') params.push(`logoWidth=${logoWidth}`);
                if (link) params.push(`link=${encodeURIComponent(link)}`);

                if (params.length > 0) {
                    url += '?' + params.join('&');
                }

                this.generatedUrl.value = url;
            }

            async copyToClipboard() {
                try {
                    await navigator.clipboard.writeText(this.generatedUrl.value);
                    this.copyBtn.textContent = '✅ 已复制!';
                    setTimeout(() => {
                        this.copyBtn.textContent = '📋 复制URL';
                    }, 2000);
                } catch (err) {
                    // 降级方案
                    this.generatedUrl.select();
                    document.execCommand('copy');
                    this.copyBtn.textContent = '✅ 已复制!';
                    setTimeout(() => {
                        this.copyBtn.textContent = '📋 复制URL';
                    }, 2000);
                }
            }
        }

        // 初始化徽章生成器
        document.addEventListener('DOMContentLoaded', () => {
            new BadgeGenerator();
        });
    </script>
</body>
</html>
