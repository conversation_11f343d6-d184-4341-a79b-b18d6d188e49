# 月子中心发展历程
class MaternityCenterDevelopmentHistory(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True,related_name='development_histories')
    # 年份
    year = models.IntegerField(verbose_name="年份", help_text="月子中心发展历程年份")
    # 发展文本
    description = models.TextField(verbose_name="发展文本", help_text="月子中心发展文本",default="")
    
    class Meta:
        verbose_name = "月子中心发展历程"
        verbose_name_plural = "月子中心发展历程"
        ordering = ['-year']

# 月子中心品牌介绍
class MaternityCenterBrandIntroduction(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True,related_name='brand_introductions')
    # 介绍图
    introduction_image = models.FileField(upload_to=introduction_image_upload_path, verbose_name="介绍图", blank=True, null=True)
    # 品牌简介
    brand_introduction = models.TextField(verbose_name="品牌简介", help_text="月子中心品牌简介",default="")
    # 品牌优势
    brand_advantage = models.TextField(verbose_name="品牌优势", help_text="月子中心品牌优势",default="")
    # 品牌愿景
    brand_vision = models.TextField(verbose_name="品牌愿景", help_text="月子中心品牌愿景",default="")
    # 荣誉资质
    honor_qualification = models.TextField(verbose_name="荣誉资质", help_text="月子中心荣誉资质",default="")
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    
    class Meta:
        verbose_name = "月子中心品牌介绍"
        verbose_name_plural = "月子中心品牌介绍"


class MaternityCenterBrandIntroductionListView(APIView):
    authentication_classes = []
    permission_classes = []

    def get(self, request,mcid):
        mci = MaternityCenterBrandIntroduction.objects.filter(maternity_center=mcid)
        serializer = MaternityCenterBrandIntroductionSerializer(mci, many=True)
        return make_response(msg='获取月子中心品牌介绍成功',data=serializer.data)


# 月子中心发展历程
class MaternityCenterDevelopmentHistorySerializer(serializers.ModelSerializer):
    
    created_at = ShanghaiFriendlyDateTimeField()
    updated_at = ShanghaiFriendlyDateTimeField()
    
    class Meta:
        model = MaternityCenterDevelopmentHistory
        fields = '__all__'

# 月子中心品牌介绍序列化器
class MaternityCenterBrandIntroductionSerializer(serializers.ModelSerializer):
    development_histories = MaternityCenterDevelopmentHistorySerializer(many=True)
    created_at = ShanghaiFriendlyDateTimeField()
    updated_at = ShanghaiFriendlyDateTimeField()
    
    class Meta:
        model = MaternityCenterBrandIntroduction
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')
        


为什么报错

  File "/Users/<USER>/Code/Mountex/月子中心/CareCenter/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 795, in data
    ret = super().data
          ^^^^^^^^^^^^
  File "/Users/<USER>/Code/Mountex/月子中心/CareCenter/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
                 ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "/Users/<USER>/Code/Mountex/月子中心/CareCenter/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 714, in to_representation
    self.child.to_representation(item) for item in iterable
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/Users/<USER>/Code/Mountex/月子中心/CareCenter/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 525, in to_representation
    attribute = field.get_attribute(instance)
  File "/Users/<USER>/Code/Mountex/月子中心/CareCenter/venv/lib/python3.13/site-packages/rest_framework/fields.py", line 470, in get_attribute
    raise type(exc)(msg)
AttributeError: Got AttributeError when attempting to get a value for field `development_histories` on serializer `MaternityCenterBrandIntroductionSerializer`.
The serializer field might be named incorrectly and not match any attribute or key on the `MaternityCenterBrandIntroduction` instance.
Original exception text was: 'MaternityCenterBrandIntroduction' object has no attribute 'development_histories'.
[18/Jun/2025 10:54:05] "GET /maternity-center/brand/introduction/1/ HTTP/1.1" 500 138680

解答问题，不要更改任何文件