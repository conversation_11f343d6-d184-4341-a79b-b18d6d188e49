<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="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" />
      </map>
    </option>
  </component>
</project>