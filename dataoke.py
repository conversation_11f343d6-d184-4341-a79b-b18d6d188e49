import argparse
import alibabacloud_oss_v2 as oss


def sign_public_url(region,endpoint,bucket,key):
    # 从环境变量中加载访问OSS所需的认证信息，用于身份验证
    credentials_provider = oss.credentials.EnvironmentVariableCredentialsProvider()

    # 使用SDK的默认配置创建配置对象，并设置认证提供者
    cfg = oss.config.load_default()
    cfg.credentials_provider = credentials_provider

    # 设置配置对象的区域属性，根据用户提供的命令行参数
    cfg.region = region

    # 设置自定义域名，例如“http://static.example.com”
    cfg.endpoint = "http://cdn.kook-ticket.xin"

    # 设置使用CNAME
    cfg.use_cname = True

    # 使用上述配置初始化OSS客户端，准备与OSS交互
    client = oss.Client(cfg)

    # 生成预签名的GET请求
    pre_result = client.presign(
        oss.GetObjectRequest(
            bucket="care-center-oss",  # 指定存储空间名称
            key="maternity/contracts/f5e6d7cf5e6d7c/2025-07/07a278cd372147788caef16a63f4d21a_20250715_f145764b.jpeg",  # 指定对象键名
        )
    )

    # 打印预签名请求的方法、过期时间和URL
    print(f'method: {pre_result.method},'
          f' expiration: {pre_result.expiration.strftime("%Y-%m-%dT%H:%M:%S.000Z")},'
          f' url: {pre_result.url}'
          )

    # 打印预签名请求的已签名头信息
    for key, value in pre_result.signed_headers.items():
        print(f'signed headers key: {key}, signed headers value: {value}')


# 当此脚本被直接执行时，调用main函数开始处理逻辑
if __name__ == "__main__":
    main()  # 脚本入口点，控制程序流程从这里开始