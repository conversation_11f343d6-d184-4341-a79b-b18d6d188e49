<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>匿名對話 SessionStorage 測試</title>
</head>
<body>
    <h2>匿名會話 SessionStorage 測試</h2>
    <div>
        <p>當前 Session ID: <span id="session-id"></span></p>
        <p>當前 Local ID: <span id="local-id"></span></p>
    </div>
    <button onclick="refreshIDs()">刷新ID</button>

    <script>
        function getOrCreateSessionID() {
            if (!sessionStorage.getItem('session_id')) {
                sessionStorage.setItem('session_id', crypto.randomUUID());
            }
            return sessionStorage.getItem('session_id');
        }

        function getOrCreateLocalID() {
            if (!localStorage.getItem('local_id')) {
                localStorage.setItem('local_id', crypto.randomUUID());
            }
            return localStorage.getItem('local_id');
        }

        function refreshIDs() {
            document.getElementById('session-id').textContent = getOrCreateSessionID();
            document.getElementById('local-id').textContent = getOrCreateLocalID();
        }

        // 初始化
        refreshIDs();
    </script>

    <p>
        <strong>測試方式：</strong><br>
        1. 刷新頁面，看 Session ID 是否變化（重開分頁會變，刷新不變）。<br>
        2. 關閉分頁重新開啟頁面，觀察 Session ID 是否重新生成（會重新生成）。<br>
        3. Local ID 則是始終不變的，除非你手動清除。
    </p>
</body>
</html>
