import json
import os
import time
from typing import List, Dict, Union, Tuple
import requests
from pyalex import Work, Works


S2_API_KEY = ''


def search_for_papers(query: str):
    import pyalex
    mail = '<EMAIL>'
    if mail is None:
        print("[WARNING] Please set OPENALEX_MAIL_ADDRESS for better access to OpenAlex API!")
    else:
        pyalex.config.email = mail

    def extract_info_from_work(work: Work, max_abstract_length: int = 99999) -> dict[str, str]:
        # "Unknown" is returned when venue is unknown...
        venue = "Unknown"
        for i, location in enumerate(work["locations"]):
            if location["source"] is not None:
                venue = location["source"]["display_name"]
                if venue != "":
                    break
        title = work["title"]
        abstract = work["abstract"]
        if abstract is None:
            abstract = ""
        if len(abstract) > max_abstract_length:
            print(f"[WARNING] {title=}: {len(abstract)=} is too long! Use first {max_abstract_length} chars.")
            abstract = abstract[:max_abstract_length]
        authors_list = [author["author"]["display_name"] for author in work["authorships"]]
        authors = " and ".join(authors_list) if len(authors_list) < 20 else f"{authors_list[0]} et al."
        paper = dict(
            title=title,
            authors=authors,
            venue=venue,
            year=work["publication_year"],
            abstract=abstract,
            citationCount=work["cited_by_count"],
        )
        return paper

    # 设置查询并直接使用cursor参数
    works_query = Works().search(query).paginate(per_page=10)

    for w in works_query:
        for s in w:
            print(s)



# 使用示例
if __name__ == "__main__":
    # 单次查询示例
    # papers, next_cursor = search_for_papers("Non-stick coating", 10)
    # print(f"First batch: {json.dumps(papers, ensure_ascii=False)}")
    # print(f"Next cursor: {next_cursor}")

    # 批量查询示例
    all_papers = search_for_papers("Non-stick coating")

