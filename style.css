@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap'); /* 使用一个看起来相似的字体 */

:root {
    --primary-purple: #8a4dff; /* 紫色主色调 (近似值) */
    --light-purple-bg: #f4efff; /* 连接钱包部分的浅紫色背景 (近似值) */
    --feature-bg: #ffffff;    /* 特性卡片的背景色 */
    --text-dark: #1a1a1a;     /* 深色文字 */
    --text-light: #555555;    /* 浅色文字 */
    --icon-bg: var(--primary-purple); /* 图标背景 */
    --border-radius-medium: 12px; /* 中等圆角 */
    --border-radius-large: 16px;  /* 大圆角 */
}

body {
    font-family: 'Inter', sans-serif; /* 应用字体 */
    background-color: #f9faff; /* 页面背景色 (近似值) */
    margin: 0;
    padding: 40px 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    color: var(--text-dark);
    box-sizing: border-box;
}

.container {
    max-width: 900px; /* 限制最大宽度 */
    width: 100%;
    text-align: center; /* 内部元素居中 */
}

/* Logo 部分 */
.logo-container {
    display: inline-flex; /* 使图标和文字在一行 */
    align-items: center;
    gap: 10px; /* 图标和文字间距 */
    margin-bottom: 40px;
}

.logo-icon {
    font-size: 2.5rem; /* 图标大小 */
    color: var(--primary-purple); /* 图标颜色 */
}

.logo-text {
    font-size: 1.8rem;
    font-weight: 700; /* 加粗 */
    color: var(--primary-purple); /* 文字颜色 */
    letter-spacing: 1px; /* 轻微增加字间距 */
}


/* 主要内容 */
h1 {
    font-size: 2.5rem; /* 主标题字体大小 */
    font-weight: 700;
    margin-bottom: 15px;
    line-height: 1.3;
}

.subtitle {
    font-size: 1rem;
    color: var(--text-light);
    max-width: 600px; /* 限制副标题宽度 */
    margin: 0 auto 40px auto; /* 居中并添加底部间距 */
    line-height: 1.6;
}

/* 连接钱包部分 */
.connect-wallet-section {
    background-color: var(--light-purple-bg);
    padding: 40px;
    border-radius: var(--border-radius-large);
    max-width: 400px; /* 限制宽度 */
    margin: 0 auto 50px auto; /* 居中并添加底部间距 */
    box-shadow: 0 4px 15px rgba(138, 77, 255, 0.1); /* 添加柔和阴影 */
}

.wallet-icon {
    font-size: 2.5rem;
    color: var(--primary-purple);
    margin-bottom: 15px;
}

.connect-wallet-section h2 {
    font-size: 1.4rem;
    font-weight: 500;
    margin-top: 0;
    margin-bottom: 25px;
}

.connect-button {
    background-color: var(--primary-purple);
    color: white;
    border: none;
    padding: 15px 35px;
    font-size: 1rem;
    font-weight: 500;
    border-radius: var(--border-radius-medium);
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.connect-button:hover {
    background-color: #7030e0; /* 鼠标悬停时变深 */
}

/* 特性部分 */
.features-section {
    display: flex;
    justify-content: space-between; /* 均匀分布 */
    gap: 20px; /* 卡片间距 */
    flex-wrap: wrap; /* 在小屏幕上换行 */
}

.feature-box {
    background-color: var(--feature-bg);
    padding: 25px;
    border-radius: var(--border-radius-large);
    flex: 1; /* 让卡片平均分配空间 */
    min-width: 180px; /* 最小宽度 */
    text-align: left; /* 卡片内文字左对齐 */
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05); /* 添加柔和阴影 */
    display: flex;
    flex-direction: column; /* 使内容垂直排列 */
}

.feature-icon-wrapper {
    background-color: var(--icon-bg);
    width: 36px;
    height: 36px;
    border-radius: 50%; /* 圆形背景 */
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 15px;
}

.feature-icon {
    color: white; /* 图标颜色 */
    font-size: 1rem;
}

.feature-box h4 {
    font-size: 1.1rem;
    font-weight: 700;
    margin-top: 0;
    margin-bottom: 8px;
}

.feature-box p {
    font-size: 0.9rem;
    color: var(--text-light);
    line-height: 1.5;
    margin: 0;
}

/* 响应式设计 (可选) */
@media (max-width: 768px) {
    h1 {
        font-size: 2rem;
    }
    .features-section {
        flex-direction: column; /* 垂直堆叠 */
        align-items: center; /* 居中对齐 */
    }
    .feature-box {
       width: 80%; /* 在堆叠时调整宽度 */
       max-width: 400px;
       text-align: center; /* 文本居中 */
       align-items: center; /* 使图标也居中 */
    }
}

@media (max-width: 480px) {
    body {
        padding: 20px 10px;
    }
    h1 {
        font-size: 1.8rem;
    }
    .subtitle {
        font-size: 0.9rem;
    }
    .connect-wallet-section {
        padding: 30px;
    }
    .connect-button {
        padding: 12px 30px;
    }
    .feature-box {
        width: 95%; /* 在更小屏幕上使用更多宽度 */
        padding: 20px;
    }
     .logo-icon {
        font-size: 2rem;
    }
    .logo-text {
        font-size: 1.5rem;
    }
}