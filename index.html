<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Augment API 网络仪表盘</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap');

        /* --- Dark Theme (Default) --- */
        :root {
            --bg-color: #1a1a1a;
            --panel-bg-color: #242424;
            --list-bg-color: #2c2c2c;
            --text-color: #e0e0e0;
            --text-secondary-color: #aaa;
            --primary-color: #4ec9b0; /* VS Code Teal */
            --secondary-color: #007acc; /* VS Code Blue */
            --border-color: #444;
            --shadow-color: rgba(0, 0, 0, 0.2);
            --success-color: #4ec9b0;
            --error-color: #f44747;
            --pending-color: #888;
            --testing-color: #d7ba7d; /* VS Code Yellow */
            --best-row-bg: rgba(78, 201, 176, 0.15);
        }

        /* --- Light Theme --- */
        body.light-theme {
            --bg-color: #f4f7f9;
            --panel-bg-color: #ffffff;
            --list-bg-color: #f4f7f9; /* Match body bg for seamless look */
            --text-color: #333333;
            --text-secondary-color: #666;
            --primary-color: #00bfa5;
            --secondary-color: #007acc;
            --border-color: #e0e0e0;
            --shadow-color: rgba(0, 0, 0, 0.08);
            --success-color: #00bfa5;
            --error-color: #e53935;
            --pending-color: #999;
            --testing-color: #ffa000;
            --best-row-bg: rgba(0, 191, 165, 0.1);
        }

        html, body {
            height: 100%; margin: 0; overflow: hidden;
            font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--bg-color); color: var(--text-color);
            transition: background-color 0.3s, color 0.3s;
        }

        .main-container { display: flex; height: 100vh; }

        /* --- Left Panel --- */
        .left-panel {
            width: 380px; flex-shrink: 0; background-color: var(--panel-bg-color);
            padding: 1.5rem 2rem; box-sizing: border-box; display: flex; flex-direction: column;
            border-right: 1px solid var(--border-color); transition: background-color 0.3s, border-color 0.3s;
            box-shadow: 2px 0 15px var(--shadow-color);
            position: relative;
        }
        .theme-toggle {
            position: absolute; top: 1.5rem; right: 1.5rem;
            cursor: pointer; font-size: 1.5rem; line-height: 1;
            color: var(--text-secondary-color);
        }
        .theme-toggle .moon { display: none; }
        body.light-theme .theme-toggle .sun { display: none; }
        body.light-theme .theme-toggle .moon { display: block; }
        .left-panel header { margin-bottom: 1.5rem; flex-shrink: 0; }
        .left-panel h1 { color: var(--primary-color); margin: 0; font-size: 1.8rem; }
        .left-panel header p { color: var(--text-secondary-color); margin-top: 0.5rem; font-size: 0.9rem; }
        .left-panel .controls { margin-bottom: 1.5rem; flex-shrink: 0; }
        #start-test-btn {
            width: 100%; background-color: var(--secondary-color); color: white; border: none;
            padding: 12px 24px; font-size: 1rem; font-weight: 500; border-radius: 8px; cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
        }
        #start-test-btn:hover { background-color: #005a9e; }
        #start-test-btn:disabled { background-color: #555; cursor: not-allowed; }
        .progress-container { width: 100%; background-color: var(--border-color); border-radius: 8px; overflow: hidden; height: 8px; margin-top: 1rem; }
        #progress-bar { width: 0%; height: 100%; background-color: var(--secondary-color); transition: width 0.3s linear; }

        .left-panel-scrollable-content {
            flex-grow: 1; overflow-y: auto; min-height: 0;
        }

        .analysis-panel, .top-ranking-panel {
            background-color: var(--bg-color); padding: 1rem; border-radius: 8px;
            border: 1px solid var(--border-color);
        }
        .analysis-panel { margin-bottom: 1rem; }

        .analysis-panel h2, .top-ranking-panel h2 {
            font-size: 1rem; color: var(--primary-color); margin: 0 0 1rem 0;
            padding-bottom: 0.5rem; border-bottom: 1px solid var(--border-color);
        }
        .analysis-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 0.8rem; }
        .analysis-item { font-size: 0.9rem; }
        .analysis-item .label { color: var(--text-secondary-color); display: block; font-size: 0.8rem; margin-bottom: 0.2rem; }
        .analysis-item .value { font-weight: 500; }
        .ranking-list { list-style: none; padding: 0; margin: 0; }
        .ranking-item {
            display: flex; justify-content: space-between; align-items: center;
            padding: 0.4rem 0; font-size: 0.9rem;
            border-bottom: 1px dashed var(--border-color);
        }
        .ranking-item:last-child { border-bottom: none; }
        .ranking-item .rank-num { font-weight: bold; color: var(--primary-color); margin-right: 0.5rem; }
        .ranking-item .domain-name-rank { flex-grow: 1; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
        .ranking-item .latency-rank { font-weight: 500; color: var(--success-color); }

        .summary-panel-wrapper { margin-top: 1.5rem; min-height: 170px; display: flex; align-items: center; justify-content: center; flex-shrink: 0; }
        .summary-panel { width: 100%; text-align: center; }
        .summary-panel .initial-message { color: var(--pending-color); border: 1px dashed var(--pending-color); padding: 2rem; border-radius: 8px; }
        .fastest-recommendation {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); border-radius: 8px; padding: 1.2rem;
            box-shadow: 0 4px 20px rgba(0, 122, 204, 0.3); text-align: center;
        }
        .fastest-recommendation .title { font-size: 1rem; font-weight: bold; color: white; margin-bottom: 1rem; }
        .fastest-recommendation .domain-info { background: rgba(255, 255, 255, 0.15); border-radius: 6px; padding: 10px; backdrop-filter: blur(5px); }
        .fastest-recommendation .domain-name { font-size: 1.2rem; font-weight: bold; color: white; font-family: 'Consolas', monospace; margin-bottom: 0.5rem; }
        .fastest-recommendation .domain-details { font-size: 0.85rem; color: rgba(255, 255, 255, 0.9); display: flex; justify-content: space-between; }
        .copy-button {
            background: rgba(255, 255, 255, 0.2); border: 1px solid rgba(255, 255, 255, 0.3); color: white;
            padding: 6px 12px; border-radius: 4px; font-size: 0.8rem; cursor: pointer;
            transition: all 0.3s; margin-top: 1rem; width: 100%;
        }
        .copy-button:hover { background: rgba(255, 255, 255, 0.3); transform: translateY(-1px); }

        /* --- Right Panel --- */
        .right-panel { flex-grow: 1; background-color: var(--list-bg-color); overflow-y: auto; padding: 1.5rem; display: flex; justify-content: center; transition: background-color 0.3s; }
        #results-list { display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem; list-style: none; padding: 0; margin: 0; width: 100%; max-width: 1000px; align-content: start; }

        /* ✨ FINAL FIX: Result Item Redesign for Compactness and Centering */
        .result-item {
            display: flex;
            align-items: center;
            padding: 0.8rem 1rem;
            background-color: var(--panel-bg-color);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            transition: all 0.3s;
            box-shadow: 0 2px 5px var(--shadow-color);
            /* min-height: 50px; <--- REMOVED this problematic line */
        }
        .result-item.best-row {
            background-color: var(--best-row-bg);
            border-color: var(--primary-color);
            box-shadow: 0 4px 15px var(--shadow-color);
        }
        .result-item.best-row .domain-name { color: var(--primary-color); }

        .latency-badge {
            width: 80px;
            text-align: center;
            font-weight: 500;
            font-family: 'Consolas', monospace;
            font-size: 1rem;
            padding-right: 1rem;
            border-right: 1px solid var(--border-color);
            flex-shrink: 0;
        }

        .info-cell {
            flex-grow: 1;
            padding-left: 1rem;
            /* No flex needed here, parent's align-items handles vertical centering */
        }
        .domain-name {
            font-weight: 500;
            font-size: 0.95rem;
            margin-bottom: 0.25rem; /* Re-added margin for spacing */
            display: block; /* Ensure margin applies */
        }
        .ip-address {
            font-size: 0.85rem;
            color: var(--text-secondary-color);
            font-family: 'Consolas', monospace;
        }

        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .spinner { border: 2px solid var(--border-color); border-top: 2px solid var(--secondary-color); border-radius: 50%; width: 10px; height: 10px; animation: spin 0.8s linear infinite; display: inline-block; }

        .status-pending { color: var(--pending-color); } .status-testing { color: var(--testing-color); }
        .status-success { color: var(--success-color); } .status-error { color: var(--error-color); }

        @media (max-width: 1200px) { #results-list { grid-template-columns: 1fr; } }
        @media (max-width: 900px) {
            html, body { height: auto; overflow: auto; }
            .main-container { flex-direction: column; height: auto; }
            .left-panel { width: 100%; border-right: none; border-bottom: 1px solid var(--border-color); }
            .left-panel-scrollable-content { overflow-y: visible; max-height: none; }
            .summary-panel-wrapper { margin-top: 1.5rem; }
            .right-panel { overflow-y: visible; height: auto; }
            #results-list { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <aside class="left-panel">
            <div class="theme-toggle" id="theme-toggle">
                <span class="sun">☀️</span>
                <span class="moon">🌙</span>
            </div>
            <header>
                <h1>API 网络仪表盘</h1>
                <p>并发测试 Augment d1 ~ d20 节点</p>
            </header>
            <div class="controls">
                <button id="start-test-btn">开始测速</button>
                <div class="progress-container">
                    <div id="progress-bar"></div>
                </div>
            </div>
            <div class="left-panel-scrollable-content">
                <div class="analysis-panel" id="analysis-panel"></div>
                <div class="top-ranking-panel" id="ranking-panel"></div>
            </div>
            <div class="summary-panel-wrapper">
                <div class="summary-panel" id="summary-panel"></div>
            </div>
        </aside>
        <main class="right-panel">
            <ul id="results-list"></ul>
        </main>
    </div>

<script>
// The JavaScript remains identical, no changes needed.
document.addEventListener('DOMContentLoaded', () => {
    const startBtn = document.getElementById('start-test-btn');
    const resultsList = document.getElementById('results-list');
    const progressBar = document.getElementById('progress-bar');
    const summaryPanel = document.getElementById('summary-panel');
    const analysisPanel = document.getElementById('analysis-panel');
    const rankingPanel = document.getElementById('ranking-panel');
    const themeToggle = document.getElementById('theme-toggle');

    const domains = Array.from({ length: 20 }, (_, i) => `d${i + 1}.api.augmentcode.com`);
    let testResults = [];

    const applyTheme = (theme) => {
        document.body.classList.toggle('light-theme', theme === 'light');
        localStorage.setItem('speedtest-theme', theme);
    };
    themeToggle.addEventListener('click', () => applyTheme(document.body.classList.contains('light-theme') ? 'dark' : 'light'));
    applyTheme(localStorage.getItem('speedtest-theme') || 'dark');

    const initUI = () => {
        startBtn.disabled = false;
        startBtn.textContent = '开始测速';
        resultsList.innerHTML = '';
        summaryPanel.innerHTML = '<div class="initial-message">点击 "开始测速" 按钮以启动...</div>';
        analysisPanel.innerHTML = `<h2>网络诊断报告</h2><div class="initial-message" style="padding:0; border: none; text-align:left; font-size:0.9em;">等待测试数据...</div>`;
        rankingPanel.innerHTML = `<h2>前三节点排名</h2><div class="initial-message" style="padding:0; border: none; text-align:left; font-size:0.9em;">等待测试数据...</div>`;
        progressBar.style.width = '0%';
        testResults = [];

        domains.forEach((domain) => {
            const item = document.createElement('li');
            item.className = 'result-item';
            item.id = `item-${domain}`;
            item.innerHTML = `
                <div id="latency-${domain}" class="latency-badge status-pending">---</div>
                <div class="info-cell">
                    <span class="domain-name">${domain}</span>
                    <span id="ip-${domain}" class="ip-address status-pending">IP: 等待中...</span>
                </div>`;
            resultsList.appendChild(item);
        });
    };

    async function getIPAddress(domain) {
        try {
            const res = await fetch(`https://dns.google/resolve?name=${domain}&type=A`, { signal: AbortSignal.timeout(5000) });
            if (!res.ok) return '网络错误';
            const data = await res.json();
            return (data.Answer && data.Answer.length > 0) ? data.Answer[0].data : '无记录';
        } catch { return '解析失败'; }
    }

    async function testDomain(domain) {
        document.getElementById(`latency-${domain}`).innerHTML = '<div class="spinner"></div>';
        const startTime = performance.now();
        const [latencyResult, ipResult] = await Promise.allSettled([
            (async () => {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 8000);
                try {
                    await fetch(`https://${domain}`, { signal: controller.signal, mode: 'no-cors', cache: 'no-store' });
                    return { timeMs: performance.now() - startTime, error: null };
                } catch (e) {
                    return { timeMs: 0, error: e.name === 'AbortError' ? '超时' : '连接失败' };
                } finally { clearTimeout(timeoutId); }
            })(),
            getIPAddress(domain)
        ]);

        const latency = latencyResult.status === 'fulfilled' ? latencyResult.value : { timeMs: 0, error: '异常' };
        const ip = ipResult.status === 'fulfilled' ? ipResult.value : '异常';

        const ipEl = document.getElementById(`ip-${domain}`);
        ipEl.textContent = `IP: ${ip}`;
        ipEl.className = `ip-address ${ip.includes('失败') || ip.includes('异常') || ip === '无记录' ? 'status-error' : ''}`;

        const latencyEl = document.getElementById(`latency-${domain}`);
        if (latency.error === null) {
            latencyEl.textContent = `${latency.timeMs.toFixed(0)} ms`;
            latencyEl.className = 'latency-badge status-success';
        } else {
            latencyEl.textContent = latency.error;
            latencyEl.className = 'latency-badge status-error';
        }

        return { domain, ip, latency: latency.error === null ? latency.timeMs : -1 };
    }

    const startTest = async () => {
        startBtn.disabled = true;
        startBtn.textContent = '正在测速...';
        initUI();
        let completedCount = 0;
        const testPromises = domains.map(domain =>
            testDomain(domain).then(result => {
                completedCount++;
                progressBar.style.width = `${(completedCount / domains.length) * 100}%`;
                return result;
            })
        );
        testResults = await Promise.all(testPromises);
        finalizeTest();
    };

    const finalizeTest = () => {
        const validResults = testResults.filter(r => r.latency !== -1).sort((a, b) => a.latency - b.latency);

        displayAnalysis(validResults);
        displayRanking(validResults);

        if (validResults.length > 0) {
            const best = validResults[0];
            document.getElementById(`item-${best.domain}`).classList.add('best-row');
            summaryPanel.innerHTML = `
                <div class="fastest-recommendation">
                    <div class="title">🎯 推荐最快节点</div>
                    <div class="domain-info">
                        <div class="domain-name">${best.domain}</div>
                        <div class="domain-details"><span>IP: ${best.ip}</span><span>延迟: ${best.latency.toFixed(0)} ms</span></div>
                    </div>
                    <button class="copy-button" onclick="copyToClipboard('${best.domain}', this)">📋 复制域名</button>
                </div>`;
        } else {
            summaryPanel.innerHTML = `<div class="initial-message status-error">🤷‍♂️ 所有节点均无法连接或超时。</div>`;
        }
        startBtn.disabled = false;
        startBtn.textContent = '重新测速';
    };

    function displayAnalysis(validResults) {
        let avgLatency = 'N/A', jitter = 'N/A', successRate = '0%', slowestLatency = 'N/A', uniqueIPs = '0';

        if (validResults.length > 0) {
            const latencies = validResults.map(r => r.latency);
            avgLatency = (latencies.reduce((a, b) => a + b, 0) / latencies.length).toFixed(0) + ' ms';
            const mean = parseFloat(avgLatency);
            const variance = latencies.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / latencies.length;
            jitter = Math.sqrt(variance).toFixed(0) + ' ms';
            slowestLatency = validResults[validResults.length - 1].latency.toFixed(0) + ' ms';
            uniqueIPs = new Set(validResults.map(r => r.ip)).size;
        }
        successRate = ((validResults.length / domains.length) * 100).toFixed(0) + '%';

        analysisPanel.innerHTML = `
            <h2>网络诊断报告</h2>
            <div class="analysis-grid">
                <div class="analysis-item"><span class="label">平均延迟</span><span class="value">${avgLatency}</span></div>
                <div class="analysis-item"><span class="label">网络抖动</span><span class="value">${jitter}</span></div>
                <div class="analysis-item"><span class="label">成功率</span><span class="value">${successRate}</span></div>
                <div class="analysis-item"><span class="label">最慢延迟</span><span class="value">${slowestLatency}</span></div>
                <div class="analysis-item" style="grid-column: 1 / -1;"><span class="label">独立IP数</span><span class="value">${uniqueIPs} 个</span></div>
            </div>`;
    }

    function displayRanking(validResults) {
        let rankingHtml = `<h2>前三节点排名</h2><ul class="ranking-list">`;
        if (validResults.length > 0) {
            const top3 = validResults.slice(0, 3);
            top3.forEach((node, index) => {
                rankingHtml += `
                    <li class="ranking-item">
                        <span class="rank-num">${index + 1}.</span>
                        <span class="domain-name-rank">${node.domain}</span>
                        <span class="latency-rank">${node.latency.toFixed(0)} ms</span>
                    </li>`;
            });
        } else {
            rankingHtml += `<li class="initial-message" style="padding:0; border: none; text-align:left; font-size:0.9em;">无可用排名数据。</li>`;
        }
        rankingHtml += `</ul>`;
        rankingPanel.innerHTML = rankingHtml;
    }

    startBtn.addEventListener('click', startTest);
    initUI();
});

function copyToClipboard(text, buttonElement) {
    navigator.clipboard.writeText(text).then(() => {
        const originalText = buttonElement.textContent;
        buttonElement.textContent = '✅ 已复制!';
        setTimeout(() => { buttonElement.textContent = originalText; }, 1500);
    }).catch(err => { alert('复制失败，请手动复制。'); });
}
</script>
</body>
</html>