package main

import (
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"

	"github.com/google/uuid"
)

// generateRandomHex 生成 n 个随机字节，并返回 16 进制字符串
func generateRandomHex(n int) (string, error) {
	b := make([]byte, n)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(b), nil
}

func main() {
	// 生成 telemetry.devDeviceId，使用标准的 UUID 格式
	devDeviceId := uuid.New().String()

	// 生成 telemetry.macMachineId：64 字节随机数（128 个 16 进制字符）
	macMachineId, err := generateRandomHex(64)
	if err != nil {
		log.Fatalf("生成 macMachineId 失败: %v", err)
	}

	// 生成 telemetry.machineId：32 字节随机数（64 个 16 进制字符）
	machineId, err := generateRandomHex(32)
	if err != nil {
		log.Fatalf("生成 machineId 失败: %v", err)
	}

	// 生成 telemetry.sqmId：64 字节随机数（128 个 16 进制字符）
	sqmId, err := generateRandomHex(64)
	if err != nil {
		log.Fatalf("生成 sqmId 失败: %v", err)
	}

	// 将结果存入 map 中，便于生成 JSON 输出
	telemetry := map[string]string{
		"telemetry.devDeviceId":  devDeviceId,
		"telemetry.macMachineId": macMachineId,
		"telemetry.machineId":    machineId,
		"telemetry.sqmId":        sqmId,
	}

	// 美化输出 JSON
	jsonData, err := json.MarshalIndent(telemetry, "", "    ")
	if err != nil {
		log.Fatalf("JSON 编码失败: %v", err)
	}

	fmt.Println(string(jsonData))
}
